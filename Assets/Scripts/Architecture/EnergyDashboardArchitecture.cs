using QFramework;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源看板架构 - QFramework架构注册
    /// </summary>
    public class EnergyDashboardArchitecture : Architecture<EnergyDashboardArchitecture>
    {
        protected override void Init()
        {
            // 注册模型
            this.RegisterModel<IEnergyDashboardModel>(new EnergyDashboardModel());
            
            // 注册系统
            this.RegisterSystem<IEnergyDataSystem>(new EnergyDataSystem());
        }
    }
}
