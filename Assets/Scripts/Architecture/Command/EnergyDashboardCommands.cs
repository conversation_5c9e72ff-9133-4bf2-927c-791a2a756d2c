using QFramework;
using System;

namespace EnergyDashboard
{
    /// <summary>
    /// 更新筛选选择命令
    /// </summary>
    public class UpdateFilterSelectionCommand : AbstractCommand
    {
        private readonly string filterId;
        private readonly bool isSelected;

        public UpdateFilterSelectionCommand(string filterId, bool isSelected)
        {
            this.filterId = filterId;
            this.isSelected = isSelected;
        }

        protected override void OnExecute()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var filterItems = model.FilterItems.Value;
            
            // 更新筛选项选中状态
            var targetItem = filterItems.Find(item => item.Id == filterId);
            if (targetItem != null)
            {
                targetItem.IsSelected = isSelected;
                
                // 更新筛选条件
                var filterData = model.FilterData.Value;
                if (isSelected)
                {
                    if (!filterData.SelectedObjectIds.Contains(filterId))
                    {
                        filterData.SelectedObjectIds.Add(filterId);
                    }
                }
                else
                {
                    filterData.SelectedObjectIds.Remove(filterId);
                }
                
                // 触发数据更新
                model.FilterData.Value = filterData;
                
                // 调用系统重新生成数据
                this.GetSystem<IEnergyDataSystem>().RefreshDataByFilter();
            }
        }
    }

    /// <summary>
    /// 更新时间范围命令
    /// </summary>
    public class UpdateTimeRangeCommand : AbstractCommand
    {
        private readonly System.DateTime startTime;
        private readonly System.DateTime endTime;

        public UpdateTimeRangeCommand(System.DateTime startTime, System.DateTime endTime)
        {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        protected override void OnExecute()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var filterData = model.FilterData.Value;
            
            filterData.StartTime = startTime;
            filterData.EndTime = endTime;
            
            model.FilterData.Value = filterData;
            
            // 调用系统重新生成数据
            this.GetSystem<IEnergyDataSystem>().RefreshDataByFilter();
        }
    }

    /// <summary>
    /// 刷新数据命令
    /// </summary>
    public class RefreshEnergyDataCommand : AbstractCommand
    {
        protected override void OnExecute()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            model.IsLoading.Value = true;
            
            // 调用系统刷新数据
            this.GetSystem<IEnergyDataSystem>().RefreshAllData();
        }
    }

    /// <summary>
    /// 初始化数据命令
    /// </summary>
    public class InitializeEnergyDataCommand : AbstractCommand
    {
        protected override void OnExecute()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            model.IsLoading.Value = true;
            
            // 加载初始数据
            model.LoadMockData();
            
            model.IsLoading.Value = false;
        }
    }
}
