using QFramework;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源数据系统接口
    /// </summary>
    public interface IEnergyDataSystem : ISystem
    {
        void RefreshDataByFilter();
        void RefreshAllData();
    }

    /// <summary>
    /// 能源数据系统 - 处理所有数据相关的业务逻辑
    /// </summary>
    public class EnergyDataSystem : AbstractSystem, IEnergyDataSystem
    {
        protected override void OnInit()
        {
            // 系统初始化
        }

        public void RefreshDataByFilter()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var filterData = model.FilterData.Value;
            
            // 根据筛选条件重新生成数据
            GenerateFilteredKpiData(filterData);
            GenerateFilteredChartData(filterData);
            
            // 发送数据更新事件
            this.SendEvent<EnergyDataUpdatedEvent>();
        }

        public void RefreshAllData()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            
            // 模拟异步数据加载
            model.IsLoading.Value = true;
            
            // 在实际项目中，这里会调用API获取真实数据
            // 现在使用模拟数据
            UnityEngine.Coroutine coroutine = null;
            coroutine = CoroutineRunner.Instance.StartCoroutine(SimulateDataLoading(() =>
            {
                model.LoadMockData();
                model.IsLoading.Value = false;
            }));
        }

        private void GenerateFilteredKpiData(EnergyFilterData filterData)
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var selectedCount = filterData.SelectedObjectIds.Count;
            var multiplier = selectedCount > 0 ? selectedCount * 0.3f + 0.7f : 1f; // 根据选中项数量调整数据

            var kpiData = new KpiData
            {
                TotalPower = GenerateKpiItem(1250.5f * multiplier, UnityEngine.Random.Range(-10f, 10f), "kW"),
                TotalConsumption = GenerateKpiItem(8760.3f * multiplier, UnityEngine.Random.Range(-5f, 5f), "kWh"),
                Efficiency = GenerateKpiItem(Mathf.Clamp(87.6f + UnityEngine.Random.Range(-5f, 5f), 70f, 100f), UnityEngine.Random.Range(-3f, 3f), "%"),
                CarbonEmission = GenerateKpiItem(245.7f * multiplier, UnityEngine.Random.Range(-15f, 5f), "kg")
            };

            model.KpiData.Value = kpiData;
        }

        private KpiItem GenerateKpiItem(float baseValue, float trend, string unit)
        {
            return new KpiItem
            {
                Value = baseValue + UnityEngine.Random.Range(-baseValue * 0.1f, baseValue * 0.1f),
                Trend = trend,
                Unit = unit
            };
        }

        private void GenerateFilteredChartData(EnergyFilterData filterData)
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var selectedCount = filterData.SelectedObjectIds.Count;
            var multiplier = selectedCount > 0 ? selectedCount * 0.2f + 0.8f : 1f;

            var chartData = new TimeSeriesChartData
            {
                Categories = GenerateTimeCategories(filterData),
                PowerSeries = GenerateFilteredSeries(1000f * multiplier, 200f, 100f),
                ConsumptionSeries = GenerateFilteredSeries(800f * multiplier, 150f, 80f),
                EfficiencySeries = GenerateFilteredSeries(85f, 10f, 0f, 70f, 100f)
            };

            model.ChartData.Value = chartData;
        }

        private List<string> GenerateTimeCategories(EnergyFilterData filterData)
        {
            var categories = new List<string>();
            var timeSpan = filterData.EndTime - filterData.StartTime;
            var intervalHours = Math.Max(1, (int)(timeSpan.TotalHours / 24));

            for (var time = filterData.StartTime; time <= filterData.EndTime; time = time.AddHours(intervalHours))
            {
                categories.Add(time.ToString("MM/dd HH:mm"));
            }

            return categories;
        }

        private List<float> GenerateFilteredSeries(float baseValue, float variation, float timeVariationAmplitude, float minValue = 0f, float maxValue = float.MaxValue)
        {
            var series = new List<float>();
            var random = new System.Random();
            var dataPointCount = 24; // 固定24个数据点

            for (int i = 0; i < dataPointCount; i++)
            {
                var randomVariation = (float)(random.NextDouble() - 0.5) * variation;
                var timeVariation = Mathf.Sin(i * 0.5f) * timeVariationAmplitude;
                var value = baseValue + randomVariation + timeVariation;
                
                series.Add(Mathf.Clamp(value, minValue, maxValue));
            }

            return series;
        }

        private System.Collections.IEnumerator SimulateDataLoading(System.Action onComplete)
        {
            // 模拟网络延迟
            yield return new UnityEngine.WaitForSeconds(UnityEngine.Random.Range(0.5f, 1.5f));
            onComplete?.Invoke();
        }
    }

    /// <summary>
    /// 协程运行器 - 用于在非MonoBehaviour类中运行协程
    /// </summary>
    public class CoroutineRunner : MonoBehaviour
    {
        private static CoroutineRunner _instance;
        public static CoroutineRunner Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("CoroutineRunner");
                    _instance = go.AddComponent<CoroutineRunner>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
    }
}
