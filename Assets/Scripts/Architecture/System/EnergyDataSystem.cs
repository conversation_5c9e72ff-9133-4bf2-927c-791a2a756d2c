using QFramework;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源数据系统接口
    /// </summary>
    public interface IEnergyDataSystem : ISystem
    {
        void RefreshDataByFilter();
        void RefreshAllData();
    }

    /// <summary>
    /// 能源数据系统 - 处理所有数据相关的业务逻辑
    /// </summary>
    public class EnergyDataSystem : AbstractSystem, IEnergyDataSystem
    {
        protected override void OnInit()
        {
            // 系统初始化
        }

        public void RefreshDataByFilter()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var filterData = model.FilterData.Value;
            
            // 根据筛选条件重新生成数据
            GenerateFilteredKpiData(filterData);
            GenerateFilteredChartData(filterData);
            
            // 发送数据更新事件
            this.SendEvent<EnergyDataUpdatedEvent>();
        }

        public void RefreshAllData()
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            
            // 模拟异步数据加载
            model.IsLoading.Value = true;
            
            // 在实际项目中，这里会调用API获取真实数据
            // 现在使用模拟数据
            UnityEngine.Coroutine coroutine = null;
            coroutine = CoroutineRunner.Instance.StartCoroutine(SimulateDataLoading(() =>
            {
                model.LoadMockData();
                model.IsLoading.Value = false;
            }));
        }

        private void GenerateFilteredKpiData(EnergyFilterData filterData)
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var selectedCount = filterData.SelectedObjectIds.Count;
            var multiplier = selectedCount > 0 ? selectedCount * 0.3f + 0.7f : 1f;

            var kpiData = new KpiData
            {
                TotalPower = new KpiItem { Value = 1250.5f * multiplier, Trend = UnityEngine.Random.Range(-10f, 10f), Unit = "kW" },
                TotalConsumption = new KpiItem { Value = 8760.3f * multiplier, Trend = UnityEngine.Random.Range(-5f, 5f), Unit = "kWh" },
                Efficiency = new KpiItem { Value = Mathf.Clamp(87.6f + UnityEngine.Random.Range(-5f, 5f), 0f, 100f), Trend = UnityEngine.Random.Range(-3f, 3f), Unit = "%" },
                CarbonEmission = new KpiItem { Value = 245.7f * multiplier, Trend = UnityEngine.Random.Range(-15f, 5f), Unit = "kg" }
            };

            model.KpiData.Value = kpiData;
        }

        private void GenerateFilteredChartData(EnergyFilterData filterData)
        {
            var model = this.GetModel<IEnergyDashboardModel>();
            var selectedCount = filterData.SelectedObjectIds.Count;
            var multiplier = selectedCount > 0 ? selectedCount * 0.2f + 0.8f : 1f;

            var chartData = new TimeSeriesChartData
            {
                Categories = GenerateTimeCategories(filterData),
                PowerSeries = GenerateFilteredSeries(1000f * multiplier, 200f, 100f),
                ConsumptionSeries = GenerateFilteredSeries(800f * multiplier, 150f, 80f),
                EfficiencySeries = GenerateFilteredSeries(85f, 10f, 0f, 70f, 100f)
            };

            model.ChartData.Value = chartData;
        }

        private List<string> GenerateTimeCategories(EnergyFilterData filterData)
        {
            var categories = new List<string>();
            var startTime = filterData.StartTime;
            var endTime = filterData.EndTime;
            var timeSpan = endTime - startTime;
            var intervalHours = Math.Max(1, (int)(timeSpan.TotalHours / 24));

            for (var time = startTime; time <= endTime; time = time.AddHours(intervalHours))
            {
                categories.Add(time.ToString("MM/dd HH:mm"));
            }

            return categories;
        }

        private List<float> GenerateFilteredSeries(float baseValue, float variance, float minValue, float? minClamp = null, float? maxClamp = null)
        {
            var series = new List<float>();
            var random = new System.Random();
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-(int)variance, (int)variance);
                value = Math.Max(value, minValue);
                
                if (minClamp.HasValue)
                    value = Math.Max(value, minClamp.Value);
                if (maxClamp.HasValue)
                    value = Math.Min(value, maxClamp.Value);
                    
                series.Add(value);
            }
            
            return series;
        }

        private System.Collections.IEnumerator SimulateDataLoading(System.Action onComplete)
        {
            // 模拟网络延迟
            yield return new UnityEngine.WaitForSeconds(UnityEngine.Random.Range(0.5f, 1.5f));
            onComplete?.Invoke();
        }
    }

    /// <summary>
    /// 协程运行器 - 用于在非MonoBehaviour类中运行协程
    /// </summary>
    public class CoroutineRunner : MonoBehaviour
    {
        private static CoroutineRunner _instance;
        public static CoroutineRunner Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("CoroutineRunner");
                    _instance = go.AddComponent<CoroutineRunner>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }
    }
}
