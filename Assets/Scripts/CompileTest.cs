using UnityEngine;
using QFramework;
using EnergyDashboard;
using XCharts.Runtime;

/// <summary>
/// 编译测试脚本 - 验证所有类型和引用是否正确
/// </summary>
public class CompileTest : MonoBehaviour, IController
{
    [Header("测试XCharts组件")]
    [SerializeField] private BarChart barChart;
    [SerializeField] private LineChart lineChart;
    [SerializeField] private PieChart pieChart;
    [SerializeField] private RingChart ringChart; // 使用RingChart替代GaugeChart

    public IArchitecture GetArchitecture()
    {
        return EnergyDashboardArchitecture.Interface;
    }

    void Start()
    {
        Debug.Log("[CompileTest] 编译测试通过！");
        Debug.Log("[CompileTest] 所有XCharts组件类型正确");
        Debug.Log("[CompileTest] QFramework架构正常");
        Debug.Log("[CompileTest] EnergyDashboard命名空间正常");
        
        // 测试架构初始化
        TestArchitecture();
        
        // 测试XCharts组件
        TestXChartsComponents();
    }

    [ContextMenu("测试架构")]
    void TestArchitecture()
    {
        try
        {
            EnergyDashboardArchitecture.InitArchitecture();
            
            var model = this.GetModel<IEnergyDashboardModel>();
            var system = this.GetSystem<IEnergyDataSystem>();
            
            Debug.Log($"[CompileTest] Model注册成功: {model != null}");
            Debug.Log($"[CompileTest] System注册成功: {system != null}");
            
            // 测试命令
            this.SendCommand<InitializeEnergyDataCommand>();
            Debug.Log("[CompileTest] 命令发送成功");
            
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[CompileTest] 架构测试失败: {ex.Message}");
        }
    }

    [ContextMenu("测试XCharts组件")]
    void TestXChartsComponents()
    {
        Debug.Log("[CompileTest] XCharts组件测试:");
        Debug.Log($"  - BarChart: {barChart != null}");
        Debug.Log($"  - LineChart: {lineChart != null}");
        Debug.Log($"  - PieChart: {pieChart != null}");
        Debug.Log($"  - RingChart: {ringChart != null}");
        
        // 测试RingChart API
        if (ringChart != null)
        {
            try
            {
                ringChart.ClearData();
                ringChart.AddData(0, 75f, 100f, "测试数据");
                ringChart.RefreshChart();
                Debug.Log("[CompileTest] RingChart API测试成功");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[CompileTest] RingChart API测试失败: {ex.Message}");
            }
        }
    }

    [ContextMenu("测试数据结构")]
    void TestDataStructures()
    {
        Debug.Log("[CompileTest] 数据结构测试:");
        
        // 测试KPI数据
        var kpiData = new KpiData
        {
            TotalPower = new KpiItem { Value = 1000f, Trend = 5.2f, Unit = "kW" },
            TotalConsumption = new KpiItem { Value = 800f, Trend = -2.1f, Unit = "kWh" },
            Efficiency = new KpiItem { Value = 85f, Trend = 1.8f, Unit = "%" },
            CarbonEmission = new KpiItem { Value = 200f, Trend = -8.5f, Unit = "kg" }
        };
        Debug.Log("[CompileTest] KpiData创建成功");
        
        // 测试图表数据
        var chartData = new TimeSeriesChartData();
        chartData.Categories.Add("测试类别");
        chartData.PowerSeries.Add(100f);
        chartData.ConsumptionSeries.Add(80f);
        chartData.EfficiencySeries.Add(85f);
        Debug.Log("[CompileTest] TimeSeriesChartData创建成功");
        
        // 测试筛选项
        var filterItem = new SimpleFilterItem
        {
            Id = "test_1",
            Name = "测试筛选项",
            Type = FilterItemType.Area,
            IsSelected = true
        };
        Debug.Log("[CompileTest] SimpleFilterItem创建成功");
        
        // 测试事件
        var eventData = new EnergyDataUpdatedEvent();
        Debug.Log("[CompileTest] EnergyDataUpdatedEvent创建成功");
        
        Debug.Log("[CompileTest] 所有数据结构测试通过！");
    }
}
