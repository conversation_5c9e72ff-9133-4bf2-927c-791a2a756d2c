using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;

namespace EnergyDashboard
{
    /// <summary>
    /// 筛选项UI组件 - 用于显示单个筛选条件
    /// </summary>
    public class FilterItemUI : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private Toggle toggle;
        [SerializeField] private TextMeshProUGUI nameText;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image iconImage;

        [Header("样式设置")]
        [SerializeField] private Color normalColor = new Color(0.2f, 0.2f, 0.25f, 0.8f);
        [SerializeField] private Color selectedColor = new Color(0.2f, 0.6f, 1f, 0.3f);
        [SerializeField] private Color areaIconColor = new Color(0.4f, 0.8f, 1f);
        [SerializeField] private Color deviceIconColor = new Color(1f, 0.8f, 0.4f);

        private SimpleFilterItem filterData;
        private Action<string, bool> onSelectionChanged;

        private void Awake()
        {
            // 自动获取组件
            if (toggle == null)
                toggle = GetComponent<Toggle>();
            if (nameText == null)
                nameText = GetComponentInChildren<TextMeshProUGUI>();
            if (backgroundImage == null)
                backgroundImage = GetComponent<Image>();

            // 绑定Toggle事件
            if (toggle != null)
            {
                toggle.onValueChanged.AddListener(OnToggleValueChanged);
            }
        }

        /// <summary>
        /// 设置筛选项数据和回调
        /// </summary>
        public void Setup(SimpleFilterItem data, Action<string, bool> selectionCallback)
        {
            filterData = data;
            onSelectionChanged = selectionCallback;

            // 更新UI显示
            UpdateDisplay();
        }

        private void UpdateDisplay()
        {
            if (filterData == null) return;

            // 设置名称
            if (nameText != null)
            {
                nameText.text = filterData.Name;
            }

            // 设置选中状态
            if (toggle != null)
            {
                toggle.isOn = filterData.IsSelected;
            }

            // 设置图标颜色（根据类型区分）
            if (iconImage != null)
            {
                iconImage.color = filterData.Type == FilterItemType.Area ? areaIconColor : deviceIconColor;
            }

            // 更新背景颜色
            UpdateBackgroundColor();
        }

        private void OnToggleValueChanged(bool isSelected)
        {
            // 更新数据
            if (filterData != null)
            {
                filterData.IsSelected = isSelected;
            }

            // 更新背景颜色
            UpdateBackgroundColor();

            // 触发回调
            onSelectionChanged?.Invoke(filterData?.Id, isSelected);
        }

        private void UpdateBackgroundColor()
        {
            if (backgroundImage != null)
            {
                backgroundImage.color = filterData?.IsSelected == true ? selectedColor : normalColor;
            }
        }

        /// <summary>
        /// 外部更新选中状态（不触发回调）
        /// </summary>
        public void SetSelected(bool selected, bool triggerCallback = false)
        {
            if (filterData != null)
            {
                filterData.IsSelected = selected;
            }

            if (toggle != null)
            {
                // 临时移除监听，避免循环调用
                toggle.onValueChanged.RemoveListener(OnToggleValueChanged);
                toggle.isOn = selected;
                toggle.onValueChanged.AddListener(OnToggleValueChanged);
            }

            UpdateBackgroundColor();

            if (triggerCallback)
            {
                onSelectionChanged?.Invoke(filterData?.Id, selected);
            }
        }

        private void OnDestroy()
        {
            if (toggle != null)
            {
                toggle.onValueChanged.RemoveListener(OnToggleValueChanged);
            }
        }
    }
}
