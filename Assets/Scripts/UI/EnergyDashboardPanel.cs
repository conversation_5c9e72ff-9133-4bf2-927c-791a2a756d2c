using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;
using System.Collections.Generic;
using XCharts.Runtime;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源看板面板 - 纯视图层，只负责渲染和用户交互
    /// </summary>
    public class EnergyDashboardPanel : Mono<PERSON><PERSON><PERSON><PERSON>, IController
    {
        [Header("KPI指标区域")]
        [SerializeField] private TextMeshProUGUI totalPowerValueText;
        [SerializeField] private TextMeshProUGUI totalPowerTrendText;
        [SerializeField] private TextMeshProUGUI totalConsumptionValueText;
        [SerializeField] private TextMeshProUGUI totalConsumptionTrendText;
        [SerializeField] private TextMeshProUGUI efficiencyValueText;
        [SerializeField] private TextMeshProUGUI efficiencyTrendText;
        [SerializeField] private TextMeshProUGUI carbonEmissionValueText;
        [SerializeField] private TextMeshProUGUI carbonEmissionTrendText;

        [Header("筛选区域")]
        [SerializeField] private Transform filterListContainer;
        [SerializeField] private GameObject filterItemPrefab;
        [SerializeField] private Button refreshButton;

        [Header("图表区域")]
        [SerializeField] private LineChart powerChart;
        [SerializeField] private LineChart consumptionChart;
        [SerializeField] private LineChart efficiencyChart;

        [Header("时间选择")]
        [SerializeField] private Button todayButton;
        [SerializeField] private Button weekButton;
        [SerializeField] private Button monthButton;

        private IEnergyDashboardModel mModel;
        private List<FilterItemUI> filterItemUIs = new List<FilterItemUI>();

        private void Start()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 绑定UI事件
            BindUIEvents();
            
            // 监听数据更新事件
            this.RegisterEvent<EnergyDataUpdatedEvent>(OnDataUpdated)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            // 监听模型数据变化
            mModel.FilterItems.RegisterWithInitValue(OnFilterItemsChanged)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            mModel.KpiData.RegisterWithInitValue(OnKpiDataChanged)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            mModel.ChartData.RegisterWithInitValue(OnChartDataChanged)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            // 初始化数据
            this.SendCommand<InitializeEnergyDataCommand>();
        }

        private void BindUIEvents()
        {
            // 绑定刷新按钮
            if (refreshButton != null)
            {
                refreshButton.onClick.AddListener(() => this.SendCommand<RefreshEnergyDataCommand>());
            }

            // 绑定时间选择按钮
            if (todayButton != null)
            {
                todayButton.onClick.AddListener(() => SelectTimeRange(System.DateTime.Today, System.DateTime.Now));
            }
            
            if (weekButton != null)
            {
                weekButton.onClick.AddListener(() => SelectTimeRange(System.DateTime.Now.AddDays(-7), System.DateTime.Now));
            }
            
            if (monthButton != null)
            {
                monthButton.onClick.AddListener(() => SelectTimeRange(System.DateTime.Now.AddDays(-30), System.DateTime.Now));
            }
        }

        private void SelectTimeRange(System.DateTime startTime, System.DateTime endTime)
        {
            this.SendCommand(new UpdateTimeRangeCommand(startTime, endTime));
        }

        private void OnDataUpdated(EnergyDataUpdatedEvent e)
        {
            UpdateView();
        }

        private void OnFilterItemsChanged(List<SimpleFilterItem> filterItems)
        {
            GenerateFilterList();
        }

        private void OnKpiDataChanged(KpiData kpiData)
        {
            UpdateKpiDisplay();
        }

        private void OnChartDataChanged(TimeSeriesChartData chartData)
        {
            UpdateChartsDisplay();
        }

        /// <summary>
        /// 核心方法：更新整个视图
        /// </summary>
        public void UpdateView()
        {
            UpdateKpiDisplay();
            UpdateChartsDisplay();
        }

        /// <summary>
        /// 重点方法：动态生成筛选列表
        /// </summary>
        private void GenerateFilterList()
        {
            if (filterListContainer == null || filterItemPrefab == null) return;

            // 清理现有的筛选项UI
            ClearFilterList();

            var filterItems = mModel.FilterItems.Value;
            if (filterItems == null) return;

            // 遍历模型中的筛选项数据，动态生成UI
            foreach (var filterItem in filterItems)
            {
                CreateFilterItemUI(filterItem);
            }
        }

        private void ClearFilterList()
        {
            foreach (var filterItemUI in filterItemUIs)
            {
                if (filterItemUI != null && filterItemUI.gameObject != null)
                {
                    DestroyImmediate(filterItemUI.gameObject);
                }
            }
            filterItemUIs.Clear();
        }

        private void CreateFilterItemUI(SimpleFilterItem filterItem)
        {
            var itemObj = Instantiate(filterItemPrefab, filterListContainer);
            var filterItemUI = itemObj.GetComponent<FilterItemUI>();
            
            if (filterItemUI == null)
            {
                filterItemUI = itemObj.AddComponent<FilterItemUI>();
            }

            // 设置筛选项数据
            filterItemUI.Setup(filterItem, OnFilterItemClicked);
            filterItemUIs.Add(filterItemUI);
        }

        private void OnFilterItemClicked(string filterId, bool isSelected)
        {
            // 发送命令，不在此处处理业务逻辑
            this.SendCommand(new SelectFilterItemCommand(filterId, isSelected));
        }

        private void UpdateKpiDisplay()
        {
            var kpiData = mModel.KpiData.Value;
            if (kpiData == null) return;

            // 更新总功率
            UpdateKpiItem(totalPowerValueText, totalPowerTrendText, kpiData.TotalPower);
            
            // 更新总能耗
            UpdateKpiItem(totalConsumptionValueText, totalConsumptionTrendText, kpiData.TotalConsumption);
            
            // 更新效率
            UpdateKpiItem(efficiencyValueText, efficiencyTrendText, kpiData.Efficiency);
            
            // 更新碳排放
            UpdateKpiItem(carbonEmissionValueText, carbonEmissionTrendText, kpiData.CarbonEmission);
        }

        private void UpdateKpiItem(TextMeshProUGUI valueText, TextMeshProUGUI trendText, KpiItem kpiItem)
        {
            if (valueText != null && kpiItem != null)
            {
                valueText.text = $"{kpiItem.Value:F1} {kpiItem.Unit}";
            }

            if (trendText != null && kpiItem != null)
            {
                var trendSymbol = kpiItem.Trend > 0 ? "↑" : kpiItem.Trend < 0 ? "↓" : "—";
                var trendColor = kpiItem.Trend > 0 ? Color.green : kpiItem.Trend < 0 ? Color.red : Color.gray;
                
                trendText.text = $"{trendSymbol} {Mathf.Abs(kpiItem.Trend):F1}%";
                trendText.color = trendColor;
            }
        }

        private void UpdateChartsDisplay()
        {
            var chartData = mModel.ChartData.Value;
            if (chartData == null) return;

            // 更新功率图表
            UpdateLineChart(powerChart, "功率趋势", chartData.Categories, chartData.PowerSeries, "kW");
            
            // 更新能耗图表
            UpdateLineChart(consumptionChart, "能耗趋势", chartData.Categories, chartData.ConsumptionSeries, "kWh");
            
            // 更新效率图表
            UpdateLineChart(efficiencyChart, "效率趋势", chartData.Categories, chartData.EfficiencySeries, "%");
        }

        private void UpdateLineChart(LineChart chart, string title, List<string> categories, List<float> data, string unit)
        {
            if (chart == null || categories == null || data == null) return;

            // 清除现有数据
            chart.RemoveData();
            
            // 设置图表标题
            chart.SetTitle(title);
            
            // 添加类别数据
            foreach (var category in categories)
            {
                chart.AddXAxisData(category);
            }
            
            // 添加数据系列
            var serie = chart.AddSerie<Line>(title);
            for (int i = 0; i < data.Count; i++)
            {
                chart.AddData(0, data[i]);
            }
            
            // 设置Y轴单位
            chart.GetChartComponent<YAxis>().axisName.name = unit;
            
            // 刷新图表
            chart.RefreshChart();
        }

        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }

        private void OnDestroy()
        {
            // 清理UI事件绑定
            if (refreshButton != null)
                refreshButton.onClick.RemoveAllListeners();
            if (todayButton != null)
                todayButton.onClick.RemoveAllListeners();
            if (weekButton != null)
                weekButton.onClick.RemoveAllListeners();
            if (monthButton != null)
                monthButton.onClick.RemoveAllListeners();
        }
    }
}
