using UnityEngine;
using QFramework;
using EnergyDashboard;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源看板测试脚本
    /// 用于测试EnergyDashboardPanel的功能
    /// </summary>
    public class EnergyDashboardTest : MonoBehaviour, IController
    {
        [Header("测试设置")]
        [SerializeField] private bool autoOpenPanel = true;
        [SerializeField] private KeyCode testKey = KeyCode.T;
        [SerializeField] private EnergyDashboardPanel dashboardPanel;

        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }

        void Start()
        {
            // 初始化架构
            EnergyDashboardArchitecture.InitArchitecture();

            Debug.Log("[EnergyDashboardTest] 架构初始化完成");

            if (autoOpenPanel && dashboardPanel != null)
            {
                // 直接激活面板GameObject
                dashboardPanel.gameObject.SetActive(true);
                Debug.Log("[EnergyDashboardTest] 能源看板面板已激活");
            }
        }

        void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                ToggleEnergyDashboardPanel();
            }
        }

        [ContextMenu("切换能源看板")]
        public void ToggleEnergyDashboardPanel()
        {
            if (dashboardPanel != null)
            {
                bool isActive = dashboardPanel.gameObject.activeSelf;
                dashboardPanel.gameObject.SetActive(!isActive);
                Debug.Log($"[EnergyDashboardTest] 能源看板面板已{(!isActive ? "激活" : "关闭")}");
            }
        }

        [ContextMenu("测试数据刷新")]
        public void TestDataRefresh()
        {
            try
            {
                this.SendCommand<RefreshEnergyDataCommand>();
                Debug.Log("[EnergyDashboardTest] 数据刷新命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardTest] 发送刷新命令时出错: {ex.Message}");
            }
        }

        [ContextMenu("测试筛选更新")]
        public void TestFilterUpdate()
        {
            try
            {
                // 测试选择第一个筛选项
                this.SendCommand(new UpdateFilterSelectionCommand("area_1", true));
                Debug.Log("[EnergyDashboardTest] 筛选更新命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardTest] 发送筛选命令时出错: {ex.Message}");
            }
        }

        [ContextMenu("打印架构状态")]
        public void PrintArchitectureStatus()
        {
            try
            {
                var model = this.GetModel<IEnergyDashboardModel>();
                var system = this.GetSystem<IEnergyDataSystem>();

                Debug.Log("[EnergyDashboardTest] 架构状态:");
                Debug.Log($"  - Model已注册: {model != null}");
                Debug.Log($"  - System已注册: {system != null}");
                Debug.Log($"  - 架构已初始化: {EnergyDashboardArchitecture.Interface != null}");

                if (model != null)
                {
                    Debug.Log($"  - KPI数据: {model.KpiData.Value != null}");
                    Debug.Log($"  - 图表数据: {model.ChartData.Value != null}");
                    Debug.Log($"  - 筛选项数量: {model.FilterItems.Value?.Count ?? 0}");
                    Debug.Log($"  - 加载状态: {model.IsLoading.Value}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardTest] 获取架构状态时出错: {ex.Message}");
            }
        }

        [ContextMenu("测试时间范围 - 今天")]
        public void TestTodayRange()
        {
            try
            {
                var today = System.DateTime.Now.Date;
                this.SendCommand(new UpdateTimeRangeCommand(today, today.AddDays(1)));
                Debug.Log("[EnergyDashboardTest] 今天时间范围命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardTest] 发送今天时间范围命令时出错: {ex.Message}");
            }
        }

        [ContextMenu("测试时间范围 - 本周")]
        public void TestWeekRange()
        {
            try
            {
                var today = System.DateTime.Now.Date;
                var weekStart = today.AddDays(-(int)today.DayOfWeek);
                this.SendCommand(new UpdateTimeRangeCommand(weekStart, weekStart.AddDays(7)));
                Debug.Log("[EnergyDashboardTest] 本周时间范围命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardTest] 发送本周时间范围命令时出错: {ex.Message}");
            }
        }
    }
}
