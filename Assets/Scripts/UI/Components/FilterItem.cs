using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 筛选项组件 - 可选择的筛选条件
    /// </summary>
    public class FilterItem : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private Button button;
        [SerializeField] private TextMeshProUGUI nameText;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image iconImage;
        
        [Header("样式设置")]
        [SerializeField] private Color normalColor = new Color(0.2f, 0.2f, 0.25f, 0.8f);
        [SerializeField] private Color selectedColor = new Color(0.2f, 0.6f, 1f, 0.3f);
        [SerializeField] private Color disabledColor = new Color(0.1f, 0.1f, 0.1f, 0.5f);
        [SerializeField] private Color textNormalColor = Color.white;
        [SerializeField] private Color textSelectedColor = new Color(0.4f, 0.8f, 1f, 1f);
        [SerializeField] private Color textDisabledColor = Color.gray;
        
        public event Action<FilterItem> OnItemClicked;
        
        public FilterType FilterType { get; private set; }
        public bool IsSelected { get; private set; }
        public bool IsEnabled { get; private set; } = true;
        
        private object itemData;
        private string itemName;
        
        private void Awake()
        {
            // 自动获取组件
            if (button == null)
                button = GetComponent<Button>();
            if (nameText == null)
                nameText = GetComponentInChildren<TextMeshProUGUI>();
            if (backgroundImage == null)
                backgroundImage = GetComponent<Image>();
            
            // 绑定点击事件
            if (button != null)
            {
                button.onClick.AddListener(OnButtonClicked);
            }
        }
        
        public void Initialize(string name, FilterType filterType)
        {
            itemName = name;
            FilterType = filterType;
            
            if (nameText != null)
            {
                nameText.text = name;
            }
            
            // 设置初始样式
            UpdateVisualState();
        }
        
        public void SetData(object data)
        {
            itemData = data;
        }
        
        public object GetData()
        {
            return itemData;
        }
        
        public void SetSelected(bool selected)
        {
            if (IsSelected == selected) return;
            
            IsSelected = selected;
            UpdateVisualState();
        }
        
        public void ToggleSelected()
        {
            SetSelected(!IsSelected);
        }
        
        public void SetEnabled(bool enabled)
        {
            if (IsEnabled == enabled) return;
            
            IsEnabled = enabled;
            
            if (button != null)
            {
                button.interactable = enabled;
            }
            
            UpdateVisualState();
        }
        
        private void OnButtonClicked()
        {
            if (!IsEnabled) return;
            
            OnItemClicked?.Invoke(this);
        }
        
        private void UpdateVisualState()
        {
            if (backgroundImage != null)
            {
                if (!IsEnabled)
                {
                    backgroundImage.color = disabledColor;
                }
                else if (IsSelected)
                {
                    backgroundImage.color = selectedColor;
                }
                else
                {
                    backgroundImage.color = normalColor;
                }
            }
            
            if (nameText != null)
            {
                if (!IsEnabled)
                {
                    nameText.color = textDisabledColor;
                }
                else if (IsSelected)
                {
                    nameText.color = textSelectedColor;
                }
                else
                {
                    nameText.color = textNormalColor;
                }
            }
            
            // 添加选中状态的视觉效果
            if (iconImage != null)
            {
                iconImage.gameObject.SetActive(IsSelected);
            }
        }
        
        // 添加悬停效果
        public void OnPointerEnter()
        {
            if (!IsEnabled || IsSelected) return;
            
            if (backgroundImage != null)
            {
                var hoverColor = normalColor;
                hoverColor.a *= 1.2f;
                backgroundImage.color = hoverColor;
            }
        }
        
        public void OnPointerExit()
        {
            if (!IsEnabled || IsSelected) return;
            
            UpdateVisualState();
        }
        
        private void OnDestroy()
        {
            if (button != null)
            {
                button.onClick.RemoveListener(OnButtonClicked);
            }
        }
    }
}
