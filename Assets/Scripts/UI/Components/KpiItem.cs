using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// KPI指标项组件 - 显示单个关键指标
    /// </summary>
    public class KpiItem : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI valueText;
        [SerializeField] private TextMeshProUGUI unitText;
        [SerializeField] private TextMeshProUGUI changeText;
        [SerializeField] private Image iconImage;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image progressBar;
        [SerializeField] private GameObject changeIndicator;
        [SerializeField] private Image changeArrow;
        
        [Header("样式设置")]
        [SerializeField] private Color positiveChangeColor = new Color(0.2f, 0.8f, 0.4f);
        [SerializeField] private Color negativeChangeColor = new Color(0.8f, 0.3f, 0.2f);
        [SerializeField] private Color neutralChangeColor = Color.gray;
        
        private KpiConfig config;
        private float currentValue;
        private float targetValue;
        private Tween valueTween;
        
        public void Initialize(KpiConfig kpiConfig)
        {
            config = kpiConfig;
            
            // 设置标题
            if (titleText != null)
            {
                titleText.text = config.Title;
            }
            
            // 设置单位
            if (unitText != null)
            {
                unitText.text = config.Unit;
            }
            
            // 设置颜色主题
            if (backgroundImage != null)
            {
                var bgColor = config.Color;
                bgColor.a = 0.1f;
                backgroundImage.color = bgColor;
            }
            
            if (progressBar != null)
            {
                progressBar.color = config.Color;
            }
            
            // 设置图标（如果有的话）
            SetupIcon();
            
            // 初始化数值
            SetValue(0, 0, false);
        }
        
        private void SetupIcon()
        {
            if (iconImage == null) return;
            
            // 这里可以根据config.Icon加载对应的图标
            // 暂时使用颜色来区分
            iconImage.color = config.Color;
        }
        
        public void UpdateValue(float newValue, float change, float animationDuration = 0.5f)
        {
            targetValue = newValue;
            
            // 停止之前的动画
            if (valueTween != null && valueTween.IsActive())
            {
                valueTween.Kill();
            }
            
            // 数值动画
            if (animationDuration > 0)
            {
                valueTween = DOTween.To(() => currentValue, x => currentValue = x, targetValue, animationDuration)
                    .SetEase(Ease.OutQuart)
                    .OnUpdate(UpdateValueDisplay);
            }
            else
            {
                currentValue = targetValue;
                UpdateValueDisplay();
            }
            
            // 更新变化指示
            UpdateChangeIndicator(change);
            
            // 更新进度条
            UpdateProgressBar();
        }
        
        private void UpdateValueDisplay()
        {
            if (valueText != null)
            {
                // 根据数值大小选择合适的显示格式
                string formattedValue = FormatValue(currentValue);
                valueText.text = formattedValue;
            }
        }
        
        private string FormatValue(float value)
        {
            if (value >= 1000000)
            {
                return (value / 1000000f).ToString("F1") + "M";
            }
            else if (value >= 1000)
            {
                return (value / 1000f).ToString("F1") + "K";
            }
            else if (value >= 100)
            {
                return value.ToString("F0");
            }
            else
            {
                return value.ToString("F1");
            }
        }
        
        private void UpdateChangeIndicator(float change)
        {
            if (changeText == null) return;
            
            // 显示变化值
            string changeStr = "";
            Color changeColor = neutralChangeColor;
            
            if (change > 0)
            {
                changeStr = "+" + FormatValue(change);
                changeColor = positiveChangeColor;
                
                if (changeArrow != null)
                {
                    changeArrow.transform.rotation = Quaternion.Euler(0, 0, 0); // 向上箭头
                }
            }
            else if (change < 0)
            {
                changeStr = FormatValue(change);
                changeColor = negativeChangeColor;
                
                if (changeArrow != null)
                {
                    changeArrow.transform.rotation = Quaternion.Euler(0, 0, 180); // 向下箭头
                }
            }
            else
            {
                changeStr = "0";
                changeColor = neutralChangeColor;
                
                if (changeArrow != null)
                {
                    changeArrow.gameObject.SetActive(false);
                }
            }
            
            changeText.text = changeStr;
            changeText.color = changeColor;
            
            if (changeArrow != null && change != 0)
            {
                changeArrow.gameObject.SetActive(true);
                changeArrow.color = changeColor;
            }
            
            // 显示变化指示器
            if (changeIndicator != null)
            {
                changeIndicator.SetActive(change != 0);
            }
        }
        
        private void UpdateProgressBar()
        {
            if (progressBar == null) return;
            
            // 这里可以根据具体需求设置进度条的最大值
            // 暂时使用一个固定的最大值来演示
            float maxValue = GetMaxValueForKpi();
            float progress = Mathf.Clamp01(targetValue / maxValue);
            
            progressBar.DOFillAmount(progress, 0.5f).SetEase(Ease.OutQuart);
        }
        
        private float GetMaxValueForKpi()
        {
            // 根据KPI类型返回合适的最大值
            switch (config.Title)
            {
                case "储能状态":
                case "设备效率":
                    return 100f;
                case "总发电量":
                case "总用电量":
                    return 10000f;
                case "碳排放":
                    return 1000f;
                case "成本节约":
                    return 5000f;
                default:
                    return 1000f;
            }
        }
        
        public void SetValue(float value, float change, bool animate = true)
        {
            UpdateValue(value, change, animate ? 0.5f : 0f);
        }
        
        // 添加点击效果
        public void OnPointerClick()
        {
            // 播放点击动画
            transform.DOPunchScale(Vector3.one * 0.1f, 0.2f, 1, 0.5f);
            
            // 这里可以添加点击后的详细信息显示逻辑
        }
        
        private void OnDestroy()
        {
            if (valueTween != null && valueTween.IsActive())
            {
                valueTween.Kill();
            }
        }
    }
}
