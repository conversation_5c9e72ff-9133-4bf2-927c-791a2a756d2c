using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections.Generic;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 图表组件基类 - 处理各种类型的图表显示
    /// </summary>
    public class ChartComponent : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshProUGUI chartTitleText;
        [SerializeField] private Transform dataPointContainer;
        [SerializeField] private GameObject dataPointPrefab;
        [SerializeField] private LineRenderer lineRenderer;
        [SerializeField] private Transform barContainer;
        [SerializeField] private GameObject barPrefab;
        [SerializeField] private Image pieChartImage;
        
        [Header("坐标轴")]
        [SerializeField] private Transform xAxisContainer;
        [SerializeField] private Transform yAxisContainer;
        [SerializeField] private GameObject axisLabelPrefab;
        [SerializeField] private TextMeshProUGUI xAxisTitle;
        [SerializeField] private TextMeshProUGUI yAxisTitle;
        
        [Header("图表设置")]
        [SerializeField] private RectTransform chartArea;
        [SerializeField] private float chartWidth = 800f;
        [SerializeField] private float chartHeight = 300f;
        [SerializeField] private float animationDuration = 1f;
        
        private ChartType chartType;
        private Color[] chartColors;
        private List<ChartDataPoint> dataPoints = new List<ChartDataPoint>();
        private List<GameObject> visualElements = new List<GameObject>();
        
        public void Initialize(ChartType type, Color[] colors)
        {
            chartType = type;
            chartColors = colors;
            
            // 设置图表标题
            SetChartTitle();
            
            // 设置坐标轴标题
            SetAxisTitles();
            
            // 初始化图表区域
            InitializeChartArea();
        }
        
        private void SetChartTitle()
        {
            if (chartTitleText == null) return;
            
            string title = chartType switch
            {
                ChartType.Power => "功率趋势图",
                ChartType.Efficiency => "效率分析图",
                ChartType.Consumption => "能耗统计图",
                ChartType.Comparison => "设备对比图",
                _ => "数据图表"
            };
            
            chartTitleText.text = title;
        }
        
        private void SetAxisTitles()
        {
            if (xAxisTitle != null)
            {
                xAxisTitle.text = "时间";
            }
            
            if (yAxisTitle != null)
            {
                string yTitle = chartType switch
                {
                    ChartType.Power => "功率 (kW)",
                    ChartType.Efficiency => "效率 (%)",
                    ChartType.Consumption => "能耗 (kWh)",
                    ChartType.Comparison => "占比 (%)",
                    _ => "数值"
                };
                
                yAxisTitle.text = yTitle;
            }
        }
        
        private void InitializeChartArea()
        {
            if (chartArea != null)
            {
                chartArea.sizeDelta = new Vector2(chartWidth, chartHeight);
            }
        }
        
        public void UpdateData(List<ChartData> chartDataList)
        {
            if (chartDataList == null) return;
            
            // 清理现有数据
            ClearVisualElements();
            
            // 根据图表类型绘制
            switch (chartType)
            {
                case ChartType.Power:
                case ChartType.Efficiency:
                    DrawLineChart(chartDataList);
                    break;
                case ChartType.Consumption:
                    DrawBarChart(chartDataList);
                    break;
                case ChartType.Comparison:
                    DrawPieChart(chartDataList);
                    break;
            }
            
            // 更新坐标轴
            UpdateAxes(chartDataList);
            
            // 播放数据更新动画
            PlayDataUpdateAnimation();
        }
        
        private void ClearVisualElements()
        {
            foreach (var element in visualElements)
            {
                if (element != null)
                {
                    Destroy(element);
                }
            }
            visualElements.Clear();
            dataPoints.Clear();
        }
        
        private void DrawLineChart(List<ChartData> chartDataList)
        {
            if (lineRenderer == null || chartDataList.Count == 0) return;
            
            // 设置线条颜色
            if (chartColors.Length > 0)
            {
                lineRenderer.color = chartColors[0];
            }
            
            // 设置线条点数
            lineRenderer.positionCount = chartDataList.Count;
            
            // 计算点位置
            for (int i = 0; i < chartDataList.Count; i++)
            {
                var data = chartDataList[i];
                float x = (float)i / (chartDataList.Count - 1) * chartWidth;
                float y = data.Value / GetMaxValue(chartDataList) * chartHeight;
                
                Vector3 position = new Vector3(x, y, 0);
                lineRenderer.SetPosition(i, position);
                
                // 创建数据点
                CreateDataPoint(position, data, i);
            }
        }
        
        private void DrawBarChart(List<ChartData> chartDataList)
        {
            if (barContainer == null || barPrefab == null) return;
            
            float barWidth = chartWidth / chartDataList.Count * 0.8f;
            float maxValue = GetMaxValue(chartDataList);
            
            for (int i = 0; i < chartDataList.Count; i++)
            {
                var data = chartDataList[i];
                float barHeight = data.Value / maxValue * chartHeight;
                float x = (float)i / chartDataList.Count * chartWidth + barWidth / 2;
                
                // 创建柱状图条
                var barObj = Instantiate(barPrefab, barContainer);
                var barRect = barObj.GetComponent<RectTransform>();
                var barImage = barObj.GetComponent<Image>();
                
                if (barRect != null)
                {
                    barRect.sizeDelta = new Vector2(barWidth, barHeight);
                    barRect.anchoredPosition = new Vector2(x, barHeight / 2);
                }
                
                if (barImage != null && chartColors.Length > i % chartColors.Length)
                {
                    barImage.color = chartColors[i % chartColors.Length];
                }
                
                visualElements.Add(barObj);
                
                // 添加数值标签
                CreateBarLabel(barObj, data.Value, new Vector2(x, barHeight + 20));
            }
        }
        
        private void DrawPieChart(List<ChartData> chartDataList)
        {
            if (pieChartImage == null) return;
            
            // 这里可以实现饼图的绘制逻辑
            // 由于Unity内置的Image组件不直接支持饼图，
            // 可以使用自定义的PieChart组件或者通过代码生成网格
            
            // 简化实现：显示最大值对应的颜色
            if (chartDataList.Count > 0 && chartColors.Length > 0)
            {
                var maxData = chartDataList[0];
                foreach (var data in chartDataList)
                {
                    if (data.Value > maxData.Value)
                        maxData = data;
                }
                
                int colorIndex = chartDataList.IndexOf(maxData) % chartColors.Length;
                pieChartImage.color = chartColors[colorIndex];
            }
        }
        
        private void CreateDataPoint(Vector3 position, ChartData data, int index)
        {
            if (dataPointPrefab == null || dataPointContainer == null) return;
            
            var pointObj = Instantiate(dataPointPrefab, dataPointContainer);
            var pointRect = pointObj.GetComponent<RectTransform>();
            
            if (pointRect != null)
            {
                pointRect.anchoredPosition = new Vector2(position.x, position.y);
            }
            
            // 设置数据点颜色
            var pointImage = pointObj.GetComponent<Image>();
            if (pointImage != null && chartColors.Length > 0)
            {
                pointImage.color = chartColors[index % chartColors.Length];
            }
            
            // 添加悬停提示
            var tooltip = pointObj.AddComponent<ChartTooltip>();
            tooltip.SetData(data);
            
            visualElements.Add(pointObj);
        }
        
        private void CreateBarLabel(GameObject barObj, float value, Vector2 position)
        {
            var labelObj = new GameObject("BarLabel");
            labelObj.transform.SetParent(barObj.transform);
            
            var labelText = labelObj.AddComponent<TextMeshProUGUI>();
            labelText.text = value.ToString("F1");
            labelText.fontSize = 12;
            labelText.color = Color.white;
            labelText.alignment = TextAlignmentOptions.Center;
            
            var labelRect = labelObj.GetComponent<RectTransform>();
            labelRect.anchoredPosition = position;
            labelRect.sizeDelta = new Vector2(50, 20);
        }
        
        private void UpdateAxes(List<ChartData> chartDataList)
        {
            // 更新X轴标签
            UpdateXAxisLabels(chartDataList);
            
            // 更新Y轴标签
            UpdateYAxisLabels(chartDataList);
        }
        
        private void UpdateXAxisLabels(List<ChartData> chartDataList)
        {
            if (xAxisContainer == null || axisLabelPrefab == null) return;
            
            // 清理现有标签
            foreach (Transform child in xAxisContainer)
            {
                Destroy(child.gameObject);
            }
            
            // 创建新标签
            int labelCount = Mathf.Min(chartDataList.Count, 6); // 最多显示6个标签
            for (int i = 0; i < labelCount; i++)
            {
                int dataIndex = i * (chartDataList.Count - 1) / (labelCount - 1);
                var data = chartDataList[dataIndex];
                
                var labelObj = Instantiate(axisLabelPrefab, xAxisContainer);
                var labelText = labelObj.GetComponent<TextMeshProUGUI>();
                var labelRect = labelObj.GetComponent<RectTransform>();
                
                if (labelText != null)
                {
                    labelText.text = data.Label;
                }
                
                if (labelRect != null)
                {
                    float x = (float)dataIndex / (chartDataList.Count - 1) * chartWidth;
                    labelRect.anchoredPosition = new Vector2(x, 0);
                }
            }
        }
        
        private void UpdateYAxisLabels(List<ChartData> chartDataList)
        {
            if (yAxisContainer == null || axisLabelPrefab == null) return;
            
            // 清理现有标签
            foreach (Transform child in yAxisContainer)
            {
                Destroy(child.gameObject);
            }
            
            // 创建Y轴标签
            float maxValue = GetMaxValue(chartDataList);
            int labelCount = 5;
            
            for (int i = 0; i <= labelCount; i++)
            {
                var labelObj = Instantiate(axisLabelPrefab, yAxisContainer);
                var labelText = labelObj.GetComponent<TextMeshProUGUI>();
                var labelRect = labelObj.GetComponent<RectTransform>();
                
                if (labelText != null)
                {
                    float value = (float)i / labelCount * maxValue;
                    labelText.text = value.ToString("F0");
                }
                
                if (labelRect != null)
                {
                    float y = (float)i / labelCount * chartHeight;
                    labelRect.anchoredPosition = new Vector2(0, y);
                }
            }
        }
        
        private float GetMaxValue(List<ChartData> chartDataList)
        {
            if (chartDataList.Count == 0) return 100f;
            
            float max = chartDataList[0].Value;
            foreach (var data in chartDataList)
            {
                if (data.Value > max)
                    max = data.Value;
            }
            
            return max * 1.1f; // 添加10%的边距
        }
        
        private void PlayDataUpdateAnimation()
        {
            // 播放数据更新动画
            foreach (var element in visualElements)
            {
                if (element != null)
                {
                    element.transform.localScale = Vector3.zero;
                    element.transform.DOScale(Vector3.one, animationDuration)
                        .SetEase(Ease.OutBack);
                }
            }
            
            // 线条动画
            if (lineRenderer != null)
            {
                lineRenderer.material.DOFloat(1f, "_Alpha", animationDuration);
            }
        }
    }
    
    [System.Serializable]
    public struct ChartDataPoint
    {
        public Vector2 Position;
        public float Value;
        public string Label;
    }
}
