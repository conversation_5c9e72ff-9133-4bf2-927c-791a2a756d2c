using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using System;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 设备卡片组件 - 显示单个设备的详细信息
    /// </summary>
    public class DeviceCard : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshP<PERSON>UGUI deviceNameText;
        [SerializeField] private TextMeshP<PERSON>UGUI deviceTypeText;
        [SerializeField] private TextMeshProUGUI statusText;
        [SerializeField] private TextMeshProUGUI powerText;
        [SerializeField] private TextMeshProUGUI efficiencyText;
        [SerializeField] private Image statusIndicator;
        [SerializeField] private Image deviceIcon;
        [SerializeField] private Button cardButton;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Slider powerSlider;
        [SerializeField] private Slider efficiencySlider;
        
        [Header("网格视图组件")]
        [SerializeField] private GameObject gridViewContent;
        [SerializeField] private RectTransform gridContainer;
        
        [Header("列表视图组件")]
        [SerializeField] private GameObject listViewContent;
        [SerializeField] private RectTransform listContainer;
        
        [Header("状态颜色")]
        [SerializeField] private Color onlineColor = new Color(0.2f, 0.8f, 0.4f);
        [SerializeField] private Color offlineColor = new Color(0.8f, 0.3f, 0.2f);
        [SerializeField] private Color warningColor = new Color(1f, 0.8f, 0.2f);
        [SerializeField] private Color maintenanceColor = Color.gray;
        
        public event Action<DeviceCard> OnCardClicked;
        
        public DeviceData DeviceData { get; private set; }
        
        private ViewMode currentViewMode;
        private bool isSelected;
        
        private void Awake()
        {
            // 绑定点击事件
            if (cardButton != null)
            {
                cardButton.onClick.AddListener(OnCardButtonClicked);
            }
        }
        
        public void Initialize(DeviceData deviceData, ViewMode viewMode)
        {
            DeviceData = deviceData;
            currentViewMode = viewMode;
            
            // 更新设备信息
            UpdateDeviceInfo();
            
            // 设置视图模式
            SetViewMode(viewMode);
        }
        
        private void UpdateDeviceInfo()
        {
            if (DeviceData == null) return;
            
            // 设置设备名称
            if (deviceNameText != null)
            {
                deviceNameText.text = DeviceData.Name;
            }
            
            // 设置设备类型
            if (deviceTypeText != null)
            {
                deviceTypeText.text = DeviceData.Type;
            }
            
            // 设置状态
            UpdateStatus();
            
            // 设置功率
            if (powerText != null)
            {
                powerText.text = $"{DeviceData.Power:F1} kW";
            }
            
            // 设置效率
            if (efficiencyText != null)
            {
                efficiencyText.text = $"{DeviceData.Efficiency:F1}%";
            }
            
            // 更新滑块
            UpdateSliders();
            
            // 设置设备图标
            UpdateDeviceIcon();
        }
        
        private void UpdateStatus()
        {
            if (statusText != null)
            {
                statusText.text = GetStatusText(DeviceData.Status);
            }
            
            if (statusIndicator != null)
            {
                statusIndicator.color = GetStatusColor(DeviceData.Status);
            }
        }
        
        private string GetStatusText(DeviceStatus status)
        {
            switch (status)
            {
                case DeviceStatus.Online:
                    return "在线";
                case DeviceStatus.Offline:
                    return "离线";
                case DeviceStatus.Warning:
                    return "警告";
                case DeviceStatus.Maintenance:
                    return "维护";
                default:
                    return "未知";
            }
        }
        
        private Color GetStatusColor(DeviceStatus status)
        {
            switch (status)
            {
                case DeviceStatus.Online:
                    return onlineColor;
                case DeviceStatus.Offline:
                    return offlineColor;
                case DeviceStatus.Warning:
                    return warningColor;
                case DeviceStatus.Maintenance:
                    return maintenanceColor;
                default:
                    return Color.gray;
            }
        }
        
        private void UpdateSliders()
        {
            if (powerSlider != null)
            {
                float maxPower = GetMaxPowerForDevice();
                powerSlider.maxValue = maxPower;
                powerSlider.DOValue(DeviceData.Power, 0.5f);
            }
            
            if (efficiencySlider != null)
            {
                efficiencySlider.maxValue = 100f;
                efficiencySlider.DOValue(DeviceData.Efficiency, 0.5f);
            }
        }
        
        private float GetMaxPowerForDevice()
        {
            // 根据设备类型返回最大功率
            switch (DeviceData.Type)
            {
                case "光伏主机房":
                    return 1000f;
                case "重型动力机":
                    return 500f;
                case "激光焊接站":
                    return 200f;
                case "储能系统":
                    return 800f;
                default:
                    return 100f;
            }
        }
        
        private void UpdateDeviceIcon()
        {
            if (deviceIcon == null) return;
            
            // 根据设备类型设置图标颜色
            Color iconColor = GetStatusColor(DeviceData.Status);
            deviceIcon.color = iconColor;
        }
        
        public void SetViewMode(ViewMode viewMode)
        {
            currentViewMode = viewMode;
            
            // 切换显示内容
            if (gridViewContent != null)
            {
                gridViewContent.SetActive(viewMode == ViewMode.Grid);
            }
            
            if (listViewContent != null)
            {
                listViewContent.SetActive(viewMode == ViewMode.List);
            }
            
            // 调整布局
            AdjustLayoutForViewMode();
        }
        
        private void AdjustLayoutForViewMode()
        {
            var rectTransform = GetComponent<RectTransform>();
            if (rectTransform == null) return;
            
            switch (currentViewMode)
            {
                case ViewMode.Grid:
                    // 网格视图：正方形卡片
                    rectTransform.sizeDelta = new Vector2(300, 200);
                    break;
                case ViewMode.List:
                    // 列表视图：宽矩形卡片
                    rectTransform.sizeDelta = new Vector2(800, 100);
                    break;
            }
        }
        
        private void OnCardButtonClicked()
        {
            OnCardClicked?.Invoke(this);
        }
        
        public void SetSelected(bool selected)
        {
            isSelected = selected;
            
            if (backgroundImage != null)
            {
                Color bgColor = selected ? 
                    new Color(0.2f, 0.6f, 1f, 0.3f) : 
                    new Color(0.15f, 0.15f, 0.2f, 0.9f);
                
                backgroundImage.DOColor(bgColor, 0.2f);
            }
        }
        
        public void PlaySelectAnimation()
        {
            transform.DOPunchScale(Vector3.one * 0.05f, 0.2f, 1, 0.5f);
        }
        
        // 更新设备数据
        public void UpdateData(DeviceData newData)
        {
            DeviceData = newData;
            UpdateDeviceInfo();
        }
        
        // 添加悬停效果
        public void OnPointerEnter()
        {
            if (!isSelected && backgroundImage != null)
            {
                var hoverColor = new Color(0.2f, 0.2f, 0.25f, 0.9f);
                backgroundImage.DOColor(hoverColor, 0.1f);
            }
            
            transform.DOScale(Vector3.one * 1.02f, 0.1f);
        }
        
        public void OnPointerExit()
        {
            if (!isSelected && backgroundImage != null)
            {
                var normalColor = new Color(0.15f, 0.15f, 0.2f, 0.9f);
                backgroundImage.DOColor(normalColor, 0.1f);
            }
            
            transform.DOScale(Vector3.one, 0.1f);
        }
        
        private void OnDestroy()
        {
            if (cardButton != null)
            {
                cardButton.onClick.RemoveListener(OnCardButtonClicked);
            }
        }
    }
}
