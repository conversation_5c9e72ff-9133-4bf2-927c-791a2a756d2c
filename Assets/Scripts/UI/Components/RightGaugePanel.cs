using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;
using System.Collections.Generic;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 右侧仪表盘面板 - 显示实时监控仪表
    /// </summary>
    public class RightGaugePanel : MonoBehaviour, IController
    {
        [Header("UI组件")]
        [SerializeField] private Transform gaugeContainer;
        [SerializeField] private GameObject gaugePrefab;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private ScrollRect scrollRect;
        
        [Header("仪表盘配置")]
        [SerializeField] private GaugeConfig[] gaugeConfigs = new GaugeConfig[]
        {
            new GaugeConfig { Title = "总功率", Unit = "kW", MaxValue = 1000f, WarningThreshold = 800f },
            new GaugeConfig { Title = "电压", Unit = "V", MaxValue = 500f, WarningThreshold = 450f },
            new GaugeConfig { Title = "电流", Unit = "A", MaxValue = 100f, WarningThreshold = 80f },
            new GaugeConfig { Title = "温度", Unit = "°C", MaxValue = 100f, WarningThreshold = 75f },
            new GaugeConfig { Title = "湿度", Unit = "%", MaxValue = 100f, WarningThreshold = 80f },
            new GaugeConfig { Title = "储能", Unit = "%", MaxValue = 100f, WarningThreshold = 20f }
        };
        
        [Header("颜色设置")]
        [SerializeField] private Color normalColor = new Color(0.2f, 0.8f, 0.4f);
        [SerializeField] private Color warningColor = new Color(1f, 0.8f, 0.2f);
        [SerializeField] private Color dangerColor = new Color(0.8f, 0.3f, 0.2f);
        
        private List<GaugeComponent> gaugeComponents = new List<GaugeComponent>();
        private IEnergyDashboardModel mModel;
        
        public void Init()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 设置标题
            if (titleText != null)
            {
                titleText.text = "实时监控";
            }
            
            // 创建仪表盘
            CreateGauges();
            
            // 绑定数据
            BindData();
        }
        
        private void CreateGauges()
        {
            if (gaugePrefab == null || gaugeContainer == null) return;
            
            foreach (var config in gaugeConfigs)
            {
                CreateGauge(config);
            }
        }
        
        private void CreateGauge(GaugeConfig config)
        {
            var gaugeObj = Instantiate(gaugePrefab, gaugeContainer);
            var gaugeComponent = gaugeObj.GetComponent<GaugeComponent>();
            
            if (gaugeComponent == null)
            {
                gaugeComponent = gaugeObj.AddComponent<GaugeComponent>();
            }
            
            // 设置颜色主题
            var colorTheme = new GaugeColorTheme
            {
                NormalColor = normalColor,
                WarningColor = warningColor,
                DangerColor = dangerColor
            };
            
            gaugeComponent.Initialize(config, colorTheme);
            gaugeComponents.Add(gaugeComponent);
        }
        
        public void UpdateGaugeData(List<DeviceData> deviceList)
        {
            if (deviceList == null || gaugeComponents == null) return;
            
            // 计算各种监控数值
            var monitoringData = CalculateMonitoringData(deviceList);
            
            // 更新仪表盘数值
            for (int i = 0; i < gaugeComponents.Count && i < monitoringData.Count; i++)
            {
                var gauge = gaugeComponents[i];
                var data = monitoringData[i];
                
                gauge.UpdateValue(data.Value, data.ChangeRate);
            }
        }
        
        private List<MonitoringData> CalculateMonitoringData(List<DeviceData> deviceList)
        {
            var monitoringData = new List<MonitoringData>();
            
            // 计算总功率
            float totalPower = 0f;
            foreach (var device in deviceList)
            {
                if (device.Status == DeviceStatus.Online)
                {
                    totalPower += device.Power;
                }
            }
            monitoringData.Add(new MonitoringData { Value = totalPower, ChangeRate = 0.05f });
            
            // 计算平均电压（模拟数据）
            float avgVoltage = 380f + Random.Range(-20f, 20f);
            monitoringData.Add(new MonitoringData { Value = avgVoltage, ChangeRate = Random.Range(-0.02f, 0.02f) });
            
            // 计算平均电流（基于功率计算）
            float avgCurrent = totalPower > 0 ? totalPower / avgVoltage * 1000f : 0f;
            monitoringData.Add(new MonitoringData { Value = avgCurrent, ChangeRate = Random.Range(-0.03f, 0.03f) });
            
            // 环境温度（模拟数据）
            float temperature = 25f + Random.Range(-5f, 15f);
            monitoringData.Add(new MonitoringData { Value = temperature, ChangeRate = Random.Range(-0.01f, 0.01f) });
            
            // 环境湿度（模拟数据）
            float humidity = 60f + Random.Range(-10f, 20f);
            monitoringData.Add(new MonitoringData { Value = humidity, ChangeRate = Random.Range(-0.02f, 0.02f) });
            
            // 储能状态（基于设备数据计算）
            float storageLevel = CalculateStorageLevel(deviceList);
            monitoringData.Add(new MonitoringData { Value = storageLevel, ChangeRate = Random.Range(-0.01f, 0.01f) });
            
            return monitoringData;
        }
        
        private float CalculateStorageLevel(List<DeviceData> deviceList)
        {
            // 查找储能设备
            foreach (var device in deviceList)
            {
                if (device.Type.Contains("储能"))
                {
                    return device.Efficiency; // 使用效率作为储能水平
                }
            }
            
            // 如果没有储能设备，返回模拟数据
            return 75f + Random.Range(-10f, 15f);
        }
        
        // 添加仪表盘点击事件处理
        public void OnGaugeClicked(GaugeComponent gauge)
        {
            // 播放点击动画
            gauge.PlayClickAnimation();
            
            // 发送仪表盘详情查看命令
            this.SendCommand(new ShowGaugeDetailCommand(gauge.Config.Title));
        }
        
        // 播放数据更新动画
        public void PlayDataUpdateAnimation()
        {
            for (int i = 0; i < gaugeComponents.Count; i++)
            {
                var gauge = gaugeComponents[i];
                gauge.transform.DOPunchScale(Vector3.one * 0.05f, 0.3f, 1, 0.5f)
                    .SetDelay(i * 0.1f);
            }
        }
        
        // 设置仪表盘警告状态
        public void SetWarningState(string gaugeTitle, bool isWarning)
        {
            var gauge = gaugeComponents.Find(g => g.Config.Title == gaugeTitle);
            if (gauge != null)
            {
                gauge.SetWarningState(isWarning);
            }
        }
        
        private void BindData()
        {
            mModel.DeviceDataList.RegisterWithInitValue(UpdateGaugeData)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
        
        // 添加自动刷新功能
        private void Start()
        {
            // 每5秒自动刷新一次数据
            InvokeRepeating(nameof(RefreshGaugeData), 5f, 5f);
        }
        
        private void RefreshGaugeData()
        {
            if (mModel?.DeviceDataList?.Value != null)
            {
                UpdateGaugeData(mModel.DeviceDataList.Value);
                PlayDataUpdateAnimation();
            }
        }
        
        private void OnDestroy()
        {
            CancelInvoke();
        }
    }
    
    [System.Serializable]
    public struct GaugeConfig
    {
        public string Title;
        public string Unit;
        public float MaxValue;
        public float WarningThreshold;
        public float DangerThreshold;
    }
    
    [System.Serializable]
    public struct GaugeColorTheme
    {
        public Color NormalColor;
        public Color WarningColor;
        public Color DangerColor;
    }
    
    [System.Serializable]
    public struct MonitoringData
    {
        public float Value;
        public float ChangeRate;
    }
}
