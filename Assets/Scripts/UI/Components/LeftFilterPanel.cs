using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;
using System.Collections.Generic;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 左侧筛选面板 - 设备筛选和分类
    /// </summary>
    public class LeftFilterPanel : Mono<PERSON>eh<PERSON><PERSON>, IController
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private Transform filterContainer;
        [SerializeField] private GameObject filterItemPrefab;
        [SerializeField] private ScrollRect scrollRect;
        
        [Header("样式设置")]
        [SerializeField] private Color panelColor = new Color(0.15f, 0.15f, 0.2f, 0.9f);
        [SerializeField] private Color selectedColor = new Color(0.2f, 0.6f, 1f, 0.3f);
        
        private List<FilterItem> filterItems = new List<FilterItem>();
        private IEnergyDashboardModel mModel;
        
        public void Init()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 设置标题
            if (titleText != null)
            {
                titleText.text = "时间范围选择";
            }
            
            // 创建筛选项
            CreateFilterItems();
            
            // 绑定数据
            BindData();
        }
        
        private void CreateFilterItems()
        {
            if (filterItemPrefab == null || filterContainer == null) return;
            
            // 创建时间范围筛选
            CreateTimeRangeFilters();
            
            // 创建设备分类筛选
            CreateDeviceCategoryFilters();
        }
        
        private void CreateTimeRangeFilters()
        {
            var timeRanges = new[]
            {
                new { Name = "今日", Range = TimeRangeType.Today },
                new { Name = "昨日", Range = TimeRangeType.Yesterday },
                new { Name = "本周", Range = TimeRangeType.ThisWeek },
                new { Name = "本月", Range = TimeRangeType.ThisMonth },
                new { Name = "自定义", Range = TimeRangeType.Custom }
            };
            
            foreach (var timeRange in timeRanges)
            {
                var item = CreateFilterItem(timeRange.Name, FilterType.TimeRange);
                item.SetData(timeRange.Range);
                item.OnItemClicked += OnTimeRangeSelected;
            }
        }
        
        private void CreateDeviceCategoryFilters()
        {
            // 创建设备分类
            var categories = new[]
            {
                "区域筛选",
                "光伏主机房",
                "A01-重型动力机",
                "A01-重型动力机",
                "储能系统",
                "B01-激光焊接站",
                "B01-激光焊接站",
                "厂区监测"
            };
            
            foreach (var category in categories)
            {
                var item = CreateFilterItem(category, FilterType.DeviceCategory);
                item.SetData(category);
                item.OnItemClicked += OnDeviceCategorySelected;
            }
        }
        
        private FilterItem CreateFilterItem(string name, FilterType type)
        {
            var itemObj = Instantiate(filterItemPrefab, filterContainer);
            var filterItem = itemObj.GetComponent<FilterItem>();
            
            if (filterItem == null)
            {
                filterItem = itemObj.AddComponent<FilterItem>();
            }
            
            filterItem.Initialize(name, type);
            filterItems.Add(filterItem);
            
            return filterItem;
        }
        
        private void OnTimeRangeSelected(FilterItem item)
        {
            // 取消其他时间范围选择
            foreach (var filterItem in filterItems)
            {
                if (filterItem.FilterType == FilterType.TimeRange && filterItem != item)
                {
                    filterItem.SetSelected(false);
                }
            }
            
            item.SetSelected(true);
            
            // 发送时间范围变更命令
            var timeRangeType = (TimeRangeType)item.GetData();
            this.SendCommand(new ChangeTimeRangeCommand(timeRangeType));
        }
        
        private void OnDeviceCategorySelected(FilterItem item)
        {
            item.ToggleSelected();
            
            // 收集所有选中的设备分类
            var selectedCategories = new List<string>();
            foreach (var filterItem in filterItems)
            {
                if (filterItem.FilterType == FilterType.DeviceCategory && filterItem.IsSelected)
                {
                    selectedCategories.Add((string)filterItem.GetData());
                }
            }
            
            // 发送设备筛选命令
            this.SendCommand(new FilterDevicesCommand(selectedCategories));
        }
        
        public void UpdateDeviceList(List<DeviceData> deviceList)
        {
            // 根据设备列表更新筛选项状态
            foreach (var item in filterItems)
            {
                if (item.FilterType == FilterType.DeviceCategory)
                {
                    var categoryName = (string)item.GetData();
                    var hasDevices = deviceList.Exists(d => d.Category == categoryName);
                    item.SetEnabled(hasDevices);
                }
            }
        }
        
        private void BindData()
        {
            mModel.SelectedTimeRange.RegisterWithInitValue(OnTimeRangeChanged)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        private void OnTimeRangeChanged(TimeRange timeRange)
        {
            // 更新时间范围选择状态
            foreach (var item in filterItems)
            {
                if (item.FilterType == FilterType.TimeRange)
                {
                    var itemTimeRange = (TimeRangeType)item.GetData();
                    item.SetSelected(itemTimeRange == timeRange.Type);
                }
            }
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
    }
    
    public enum FilterType
    {
        TimeRange,
        DeviceCategory
    }
}
