using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;
using System.Collections.Generic;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 中部详情面板 - 显示设备详细信息和状态
    /// </summary>
    public class MiddleDetailPanel : Mono<PERSON><PERSON><PERSON><PERSON>, IController
    {
        [Header("UI组件")]
        [SerializeField] private Transform deviceContainer;
        [SerializeField] private GameObject deviceCardPrefab;
        [SerializeField] private ScrollRect scrollRect;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private Button viewModeButton;
        [SerializeField] private Toggle listViewToggle;
        [SerializeField] private Toggle gridViewToggle;
        
        [Header("布局设置")]
        [SerializeField] private GridLayoutGroup gridLayout;
        [SerializeField] private VerticalLayoutGroup listLayout;
        [SerializeField] private int gridColumns = 3;
        [SerializeField] private Vector2 gridCellSize = new Vector2(300, 200);
        [SerializeField] private Vector2 gridSpacing = new Vector2(10, 10);
        
        [Header("动画设置")]
        [SerializeField] private float animationDuration = 0.3f;
        [SerializeField] private float staggerDelay = 0.05f;
        
        private List<DeviceCard> deviceCards = new List<DeviceCard>();
        private IEnergyDashboardModel mModel;
        private ViewMode currentViewMode = ViewMode.Grid;
        
        public void Init()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 设置标题
            if (titleText != null)
            {
                titleText.text = "设备监控";
            }
            
            // 绑定视图切换
            SetupViewModeToggle();
            
            // 初始化布局
            SetupLayout();
            
            // 绑定数据
            BindData();
        }
        
        private void SetupViewModeToggle()
        {
            if (listViewToggle != null)
            {
                listViewToggle.onValueChanged.AddListener(OnListViewToggled);
            }
            
            if (gridViewToggle != null)
            {
                gridViewToggle.onValueChanged.AddListener(OnGridViewToggled);
            }
            
            if (viewModeButton != null)
            {
                viewModeButton.onClick.AddListener(ToggleViewMode);
            }
        }
        
        private void SetupLayout()
        {
            // 设置网格布局
            if (gridLayout != null)
            {
                gridLayout.constraint = GridLayoutGroup.Constraint.FixedColumnCount;
                gridLayout.constraintCount = gridColumns;
                gridLayout.cellSize = gridCellSize;
                gridLayout.spacing = gridSpacing;
            }
            
            // 根据当前视图模式设置布局
            UpdateLayoutMode();
        }
        
        private void OnListViewToggled(bool isOn)
        {
            if (isOn && currentViewMode != ViewMode.List)
            {
                currentViewMode = ViewMode.List;
                UpdateLayoutMode();
            }
        }
        
        private void OnGridViewToggled(bool isOn)
        {
            if (isOn && currentViewMode != ViewMode.Grid)
            {
                currentViewMode = ViewMode.Grid;
                UpdateLayoutMode();
            }
        }
        
        private void ToggleViewMode()
        {
            currentViewMode = currentViewMode == ViewMode.Grid ? ViewMode.List : ViewMode.Grid;
            
            // 更新切换按钮状态
            if (listViewToggle != null)
                listViewToggle.isOn = currentViewMode == ViewMode.List;
            if (gridViewToggle != null)
                gridViewToggle.isOn = currentViewMode == ViewMode.Grid;
            
            UpdateLayoutMode();
        }
        
        private void UpdateLayoutMode()
        {
            if (deviceContainer == null) return;
            
            // 启用/禁用对应的布局组件
            if (gridLayout != null)
                gridLayout.enabled = currentViewMode == ViewMode.Grid;
            if (listLayout != null)
                listLayout.enabled = currentViewMode == ViewMode.List;
            
            // 更新所有设备卡片的显示模式
            foreach (var card in deviceCards)
            {
                card.SetViewMode(currentViewMode);
            }
            
            // 播放切换动画
            PlayViewModeChangeAnimation();
        }
        
        public void UpdateDeviceDetails(List<DeviceData> deviceList)
        {
            if (deviceList == null) return;
            
            // 清理现有的设备卡片
            ClearDeviceCards();
            
            // 创建新的设备卡片
            CreateDeviceCards(deviceList);
            
            // 播放入场动画
            PlayEnterAnimation();
        }
        
        private void ClearDeviceCards()
        {
            foreach (var card in deviceCards)
            {
                if (card != null && card.gameObject != null)
                {
                    Destroy(card.gameObject);
                }
            }
            deviceCards.Clear();
        }
        
        private void CreateDeviceCards(List<DeviceData> deviceList)
        {
            if (deviceCardPrefab == null || deviceContainer == null) return;
            
            foreach (var deviceData in deviceList)
            {
                var cardObj = Instantiate(deviceCardPrefab, deviceContainer);
                var deviceCard = cardObj.GetComponent<DeviceCard>();
                
                if (deviceCard == null)
                {
                    deviceCard = cardObj.AddComponent<DeviceCard>();
                }
                
                deviceCard.Initialize(deviceData, currentViewMode);
                deviceCard.OnCardClicked += OnDeviceCardClicked;
                deviceCards.Add(deviceCard);
            }
        }
        
        private void OnDeviceCardClicked(DeviceCard card)
        {
            // 发送设备选择命令
            this.SendCommand(new SelectDeviceCommand(card.DeviceData));
            
            // 播放选择动画
            card.PlaySelectAnimation();
        }
        
        private void PlayEnterAnimation()
        {
            for (int i = 0; i < deviceCards.Count; i++)
            {
                var card = deviceCards[i];
                card.transform.localScale = Vector3.zero;
                card.transform.DOScale(Vector3.one, animationDuration)
                    .SetDelay(i * staggerDelay)
                    .SetEase(Ease.OutBack);
            }
        }
        
        private void PlayViewModeChangeAnimation()
        {
            foreach (var card in deviceCards)
            {
                card.transform.DOPunchScale(Vector3.one * 0.1f, 0.2f, 1, 0.5f);
            }
        }
        
        private void BindData()
        {
            mModel.DeviceDataList.RegisterWithInitValue(UpdateDeviceDetails)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
        
        private void OnDestroy()
        {
            if (listViewToggle != null)
                listViewToggle.onValueChanged.RemoveListener(OnListViewToggled);
            if (gridViewToggle != null)
                gridViewToggle.onValueChanged.RemoveListener(OnGridViewToggled);
            if (viewModeButton != null)
                viewModeButton.onClick.RemoveListener(ToggleViewMode);
        }
    }
    
    public enum ViewMode
    {
        Grid,
        List
    }
}
