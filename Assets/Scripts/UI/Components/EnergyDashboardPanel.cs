using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 能源看板主面板 - 管理整个UI布局
    /// </summary>
    public class EnergyDashboardPanel : UIPanel, IController
    {
        [Header("面板引用")]
        [SerializeField] private LeftFilterPanel leftPanel;
        [SerializeField] private TopKpiPanel topPanel;
        [SerializeField] private MiddleDetailPanel middlePanel;
        [SerializeField] private BottomChartPanel bottomPanel;
        [SerializeField] private RightGaugePanel rightPanel;
        
        [Header("背景设置")]
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Color backgroundColor = new Color(0.1f, 0.1f, 0.15f, 1f);
        
        private IEnergyDashboardModel mModel;
        
        protected override void OnInit()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 设置背景
            SetupBackground();
            
            // 初始化子面板
            InitializePanels();
            
            // 绑定数据
            BindData();
        }
        
        private void SetupBackground()
        {
            if (backgroundImage != null)
            {
                backgroundImage.color = backgroundColor;
            }
        }
        
        private void InitializePanels()
        {
            // 初始化左侧筛选面板
            if (leftPanel != null)
            {
                leftPanel.Init();
            }
            
            // 初始化顶部KPI面板
            if (topPanel != null)
            {
                topPanel.Init();
            }
            
            // 初始化中部详情面板
            if (middlePanel != null)
            {
                middlePanel.Init();
            }
            
            // 初始化底部图表面板
            if (bottomPanel != null)
            {
                bottomPanel.Init();
            }
            
            // 初始化右侧仪表盘面板
            if (rightPanel != null)
            {
                rightPanel.Init();
            }
        }
        
        private void BindData()
        {
            // 监听数据变化
            mModel.KpiDataList.RegisterWithInitValue(OnKpiDataChanged).UnRegisterWhenGameObjectDestroyed(gameObject);
            mModel.DeviceDataList.RegisterWithInitValue(OnDeviceDataChanged).UnRegisterWhenGameObjectDestroyed(gameObject);
            mModel.ChartDataList.RegisterWithInitValue(OnChartDataChanged).UnRegisterWhenGameObjectDestroyed(gameObject);
            mModel.SelectedTimeRange.RegisterWithInitValue(OnTimeRangeChanged).UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        private void OnKpiDataChanged(System.Collections.Generic.List<KpiData> kpiList)
        {
            topPanel?.UpdateKpiData(kpiList);
        }
        
        private void OnDeviceDataChanged(System.Collections.Generic.List<DeviceData> deviceList)
        {
            leftPanel?.UpdateDeviceList(deviceList);
            middlePanel?.UpdateDeviceDetails(deviceList);
            rightPanel?.UpdateGaugeData(deviceList);
        }
        
        private void OnChartDataChanged(System.Collections.Generic.List<ChartData> chartList)
        {
            bottomPanel?.UpdateChartData(chartList);
        }
        
        private void OnTimeRangeChanged(TimeRange timeRange)
        {
            topPanel?.UpdateTimeRange(timeRange);
            // 触发数据刷新
            this.SendCommand<RefreshDataCommand>();
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
        
        protected override void OnClose()
        {
            // 清理资源
            base.OnClose();
        }
    }
}
