using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 图表提示框组件 - 显示数据点的详细信息
    /// </summary>
    public class ChartTooltip : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IPointerExitHandler
    {
        [Header("提示框组件")]
        [SerializeField] private GameObject tooltipPanel;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI valueText;
        [SerializeField] private TextMeshProUGUI timeText;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private CanvasGroup canvasGroup;
        
        [Header("样式设置")]
        [SerializeField] private Vector2 offset = new Vector2(10, 10);
        [SerializeField] private float showDelay = 0.5f;
        [SerializeField] private float animationDuration = 0.2f;
        
        private ChartData chartData;
        private RectTransform tooltipRect;
        private Canvas parentCanvas;
        private bool isShowing;
        private Coroutine showCoroutine;
        
        private void Awake()
        {
            // 自动创建提示框面板
            if (tooltipPanel == null)
            {
                CreateTooltipPanel();
            }
            
            // 获取组件引用
            tooltipRect = tooltipPanel?.GetComponent<RectTransform>();
            parentCanvas = GetComponentInParent<Canvas>();
            
            // 初始状态隐藏
            if (tooltipPanel != null)
            {
                tooltipPanel.SetActive(false);
            }
        }
        
        private void CreateTooltipPanel()
        {
            // 创建提示框GameObject
            var tooltipObj = new GameObject("ChartTooltip");
            tooltipObj.transform.SetParent(transform);
            
            // 添加RectTransform
            tooltipRect = tooltipObj.AddComponent<RectTransform>();
            tooltipRect.sizeDelta = new Vector2(150, 80);
            
            // 添加CanvasGroup用于透明度控制
            canvasGroup = tooltipObj.AddComponent<CanvasGroup>();
            canvasGroup.alpha = 0f;
            
            // 添加背景图片
            backgroundImage = tooltipObj.AddComponent<Image>();
            backgroundImage.color = new Color(0.1f, 0.1f, 0.15f, 0.9f);
            
            // 创建文本组件
            CreateTooltipTexts(tooltipObj);
            
            tooltipPanel = tooltipObj;
        }
        
        private void CreateTooltipTexts(GameObject parent)
        {
            // 创建标题文本
            var titleObj = new GameObject("TitleText");
            titleObj.transform.SetParent(parent.transform);
            titleText = titleObj.AddComponent<TextMeshProUGUI>();
            titleText.text = "数据点";
            titleText.fontSize = 12;
            titleText.color = Color.white;
            titleText.alignment = TextAlignmentOptions.Center;
            
            var titleRect = titleObj.GetComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 0.7f);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.offsetMin = Vector2.zero;
            titleRect.offsetMax = Vector2.zero;
            
            // 创建数值文本
            var valueObj = new GameObject("ValueText");
            valueObj.transform.SetParent(parent.transform);
            valueText = valueObj.AddComponent<TextMeshProUGUI>();
            valueText.text = "0.0";
            valueText.fontSize = 14;
            valueText.color = new Color(0.4f, 0.8f, 1f);
            valueText.alignment = TextAlignmentOptions.Center;
            valueText.fontStyle = FontStyles.Bold;
            
            var valueRect = valueObj.GetComponent<RectTransform>();
            valueRect.anchorMin = new Vector2(0, 0.4f);
            valueRect.anchorMax = new Vector2(1, 0.7f);
            valueRect.offsetMin = Vector2.zero;
            valueRect.offsetMax = Vector2.zero;
            
            // 创建时间文本
            var timeObj = new GameObject("TimeText");
            timeObj.transform.SetParent(parent.transform);
            timeText = timeObj.AddComponent<TextMeshProUGUI>();
            timeText.text = "00:00";
            timeText.fontSize = 10;
            timeText.color = Color.gray;
            timeText.alignment = TextAlignmentOptions.Center;
            
            var timeRect = timeObj.GetComponent<RectTransform>();
            timeRect.anchorMin = new Vector2(0, 0);
            timeRect.anchorMax = new Vector2(1, 0.4f);
            timeRect.offsetMin = Vector2.zero;
            timeRect.offsetMax = Vector2.zero;
        }
        
        public void SetData(ChartData data)
        {
            chartData = data;
            UpdateTooltipContent();
        }
        
        private void UpdateTooltipContent()
        {
            if (titleText != null)
            {
                titleText.text = chartData.Label ?? "数据点";
            }
            
            if (valueText != null)
            {
                valueText.text = FormatValue(chartData.Value);
            }
            
            if (timeText != null)
            {
                timeText.text = chartData.Timestamp.ToString("HH:mm");
            }
        }
        
        private string FormatValue(float value)
        {
            if (value >= 1000000)
            {
                return (value / 1000000f).ToString("F1") + "M";
            }
            else if (value >= 1000)
            {
                return (value / 1000f).ToString("F1") + "K";
            }
            else
            {
                return value.ToString("F1");
            }
        }
        
        public void OnPointerEnter(PointerEventData eventData)
        {
            if (showCoroutine != null)
            {
                StopCoroutine(showCoroutine);
            }
            
            showCoroutine = StartCoroutine(ShowTooltipDelayed());
        }
        
        public void OnPointerExit(PointerEventData eventData)
        {
            if (showCoroutine != null)
            {
                StopCoroutine(showCoroutine);
                showCoroutine = null;
            }
            
            HideTooltip();
        }
        
        private System.Collections.IEnumerator ShowTooltipDelayed()
        {
            yield return new WaitForSeconds(showDelay);
            ShowTooltip();
        }
        
        private void ShowTooltip()
        {
            if (isShowing || tooltipPanel == null) return;
            
            isShowing = true;
            tooltipPanel.SetActive(true);
            
            // 更新位置
            UpdateTooltipPosition();
            
            // 播放显示动画
            if (canvasGroup != null)
            {
                canvasGroup.alpha = 0f;
                canvasGroup.DOFade(1f, animationDuration);
            }
            
            if (tooltipRect != null)
            {
                tooltipRect.localScale = Vector3.zero;
                tooltipRect.DOScale(Vector3.one, animationDuration)
                    .SetEase(Ease.OutBack);
            }
        }
        
        private void HideTooltip()
        {
            if (!isShowing || tooltipPanel == null) return;
            
            isShowing = false;
            
            // 播放隐藏动画
            if (canvasGroup != null)
            {
                canvasGroup.DOFade(0f, animationDuration)
                    .OnComplete(() => tooltipPanel.SetActive(false));
            }
            
            if (tooltipRect != null)
            {
                tooltipRect.DOScale(Vector3.zero, animationDuration)
                    .SetEase(Ease.InBack);
            }
        }
        
        private void UpdateTooltipPosition()
        {
            if (tooltipRect == null || parentCanvas == null) return;
            
            // 获取鼠标位置
            Vector2 mousePosition;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                parentCanvas.transform as RectTransform,
                Input.mousePosition,
                parentCanvas.worldCamera,
                out mousePosition);
            
            // 设置提示框位置
            Vector2 tooltipPosition = mousePosition + offset;
            
            // 确保提示框在屏幕范围内
            var canvasRect = parentCanvas.GetComponent<RectTransform>();
            if (canvasRect != null)
            {
                Vector2 canvasSize = canvasRect.sizeDelta;
                Vector2 tooltipSize = tooltipRect.sizeDelta;
                
                // 检查右边界
                if (tooltipPosition.x + tooltipSize.x > canvasSize.x / 2)
                {
                    tooltipPosition.x = mousePosition.x - offset.x - tooltipSize.x;
                }
                
                // 检查上边界
                if (tooltipPosition.y + tooltipSize.y > canvasSize.y / 2)
                {
                    tooltipPosition.y = mousePosition.y - offset.y - tooltipSize.y;
                }
                
                // 检查左边界
                if (tooltipPosition.x < -canvasSize.x / 2)
                {
                    tooltipPosition.x = -canvasSize.x / 2 + 10;
                }
                
                // 检查下边界
                if (tooltipPosition.y < -canvasSize.y / 2)
                {
                    tooltipPosition.y = -canvasSize.y / 2 + 10;
                }
            }
            
            tooltipRect.anchoredPosition = tooltipPosition;
        }
        
        private void Update()
        {
            // 如果提示框正在显示，实时更新位置
            if (isShowing)
            {
                UpdateTooltipPosition();
            }
        }
        
        private void OnDestroy()
        {
            if (showCoroutine != null)
            {
                StopCoroutine(showCoroutine);
            }
        }
    }
}
