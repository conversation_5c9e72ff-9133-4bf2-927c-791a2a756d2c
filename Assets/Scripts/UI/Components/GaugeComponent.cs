using UnityEngine;
using UnityEngine.UI;
using TMPro;
using DG.Tweening;
using System;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 仪表盘组件 - 显示单个监控指标的仪表盘
    /// </summary>
    public class GaugeComponent : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private TextMeshProUGUI valueText;
        [SerializeField] private TextMeshProUGUI unitText;
        [SerializeField] private TextMeshProUGUI changeText;
        [SerializeField] private Image gaugeBackground;
        [SerializeField] private Image gaugeFill;
        [SerializeField] private Image gaugePointer;
        [SerializeField] private Button gaugeButton;
        [SerializeField] private GameObject warningIndicator;
        
        [Header("仪表盘设置")]
        [SerializeField] private float minAngle = -90f;
        [SerializeField] private float maxAngle = 90f;
        [SerializeField] private float animationDuration = 1f;
        
        public event Action<GaugeComponent> OnGaugeClicked;
        
        public GaugeConfig Config { get; private set; }
        
        private GaugeColorTheme colorTheme;
        private float currentValue;
        private float targetValue;
        private bool isWarning;
        private Tween valueTween;
        private Tween pointerTween;
        
        private void Awake()
        {
            // 绑定点击事件
            if (gaugeButton != null)
            {
                gaugeButton.onClick.AddListener(OnButtonClicked);
            }
        }
        
        public void Initialize(GaugeConfig config, GaugeColorTheme theme)
        {
            Config = config;
            colorTheme = theme;
            
            // 设置标题
            if (titleText != null)
            {
                titleText.text = config.Title;
            }
            
            // 设置单位
            if (unitText != null)
            {
                unitText.text = config.Unit;
            }
            
            // 设置初始颜色
            UpdateGaugeColors();
            
            // 初始化数值
            SetValue(0, 0, false);
        }
        
        public void UpdateValue(float newValue, float changeRate, bool animate = true)
        {
            targetValue = Mathf.Clamp(newValue, 0, Config.MaxValue);
            
            // 停止之前的动画
            if (valueTween != null && valueTween.IsActive())
            {
                valueTween.Kill();
            }
            
            if (pointerTween != null && pointerTween.IsActive())
            {
                pointerTween.Kill();
            }
            
            // 数值动画
            if (animate)
            {
                valueTween = DOTween.To(() => currentValue, x => currentValue = x, targetValue, animationDuration)
                    .SetEase(Ease.OutQuart)
                    .OnUpdate(UpdateValueDisplay);
                
                // 指针动画
                AnimatePointer();
                
                // 填充动画
                AnimateFill();
            }
            else
            {
                currentValue = targetValue;
                UpdateValueDisplay();
                UpdatePointer();
                UpdateFill();
            }
            
            // 更新变化率显示
            UpdateChangeDisplay(changeRate);
            
            // 检查警告状态
            CheckWarningState();
        }
        
        private void UpdateValueDisplay()
        {
            if (valueText != null)
            {
                valueText.text = currentValue.ToString("F1");
            }
        }
        
        private void UpdateChangeDisplay(float changeRate)
        {
            if (changeText == null) return;
            
            string changeStr = "";
            Color changeColor = Color.white;
            
            if (changeRate > 0.01f)
            {
                changeStr = "↑ " + (changeRate * 100).ToString("F1") + "%";
                changeColor = colorTheme.NormalColor;
            }
            else if (changeRate < -0.01f)
            {
                changeStr = "↓ " + (Mathf.Abs(changeRate) * 100).ToString("F1") + "%";
                changeColor = colorTheme.DangerColor;
            }
            else
            {
                changeStr = "— 0.0%";
                changeColor = Color.gray;
            }
            
            changeText.text = changeStr;
            changeText.color = changeColor;
        }
        
        private void AnimatePointer()
        {
            if (gaugePointer == null) return;
            
            float targetAngle = GetAngleForValue(targetValue);
            
            pointerTween = gaugePointer.transform.DORotate(new Vector3(0, 0, targetAngle), animationDuration)
                .SetEase(Ease.OutQuart);
        }
        
        private void AnimateFill()
        {
            if (gaugeFill == null) return;
            
            float fillAmount = targetValue / Config.MaxValue;
            gaugeFill.DOFillAmount(fillAmount, animationDuration)
                .SetEase(Ease.OutQuart);
        }
        
        private void UpdatePointer()
        {
            if (gaugePointer == null) return;
            
            float angle = GetAngleForValue(currentValue);
            gaugePointer.transform.rotation = Quaternion.Euler(0, 0, angle);
        }
        
        private void UpdateFill()
        {
            if (gaugeFill == null) return;
            
            float fillAmount = currentValue / Config.MaxValue;
            gaugeFill.fillAmount = fillAmount;
        }
        
        private float GetAngleForValue(float value)
        {
            float normalizedValue = value / Config.MaxValue;
            return Mathf.Lerp(minAngle, maxAngle, normalizedValue);
        }
        
        private void CheckWarningState()
        {
            bool shouldWarn = targetValue >= Config.WarningThreshold;
            
            if (shouldWarn != isWarning)
            {
                SetWarningState(shouldWarn);
            }
        }
        
        public void SetWarningState(bool warning)
        {
            isWarning = warning;
            
            // 显示/隐藏警告指示器
            if (warningIndicator != null)
            {
                warningIndicator.SetActive(warning);
                
                if (warning)
                {
                    // 播放警告动画
                    warningIndicator.transform.DOPunchScale(Vector3.one * 0.2f, 0.5f, 2, 0.5f)
                        .SetLoops(-1, LoopType.Yoyo);
                }
                else
                {
                    warningIndicator.transform.DOKill();
                }
            }
            
            // 更新颜色
            UpdateGaugeColors();
        }
        
        private void UpdateGaugeColors()
        {
            Color currentColor;
            
            if (isWarning)
            {
                currentColor = targetValue >= Config.DangerThreshold ? 
                    colorTheme.DangerColor : colorTheme.WarningColor;
            }
            else
            {
                currentColor = colorTheme.NormalColor;
            }
            
            // 更新填充颜色
            if (gaugeFill != null)
            {
                gaugeFill.DOColor(currentColor, 0.3f);
            }
            
            // 更新指针颜色
            if (gaugePointer != null)
            {
                gaugePointer.DOColor(currentColor, 0.3f);
            }
        }
        
        public void SetValue(float value, float changeRate, bool animate = true)
        {
            UpdateValue(value, changeRate, animate);
        }
        
        public void PlayClickAnimation()
        {
            transform.DOPunchScale(Vector3.one * 0.1f, 0.3f, 1, 0.5f);
        }
        
        private void OnButtonClicked()
        {
            OnGaugeClicked?.Invoke(this);
        }
        
        // 添加悬停效果
        public void OnPointerEnter()
        {
            transform.DOScale(Vector3.one * 1.05f, 0.2f);
            
            if (gaugeBackground != null)
            {
                var hoverColor = gaugeBackground.color;
                hoverColor.a *= 1.2f;
                gaugeBackground.DOColor(hoverColor, 0.2f);
            }
        }
        
        public void OnPointerExit()
        {
            transform.DOScale(Vector3.one, 0.2f);
            
            if (gaugeBackground != null)
            {
                var normalColor = gaugeBackground.color;
                normalColor.a = 0.8f;
                gaugeBackground.DOColor(normalColor, 0.2f);
            }
        }
        
        // 添加数值格式化
        private string FormatValue(float value)
        {
            if (Config.Unit == "%" || Config.Unit == "°C")
            {
                return value.ToString("F0");
            }
            else if (value >= 1000)
            {
                return (value / 1000f).ToString("F1") + "K";
            }
            else
            {
                return value.ToString("F1");
            }
        }
        
        private void OnDestroy()
        {
            if (gaugeButton != null)
            {
                gaugeButton.onClick.RemoveListener(OnButtonClicked);
            }
            
            if (valueTween != null && valueTween.IsActive())
            {
                valueTween.Kill();
            }
            
            if (pointerTween != null && pointerTween.IsActive())
            {
                pointerTween.Kill();
            }
        }
    }
}
