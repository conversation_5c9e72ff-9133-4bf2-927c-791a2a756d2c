using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;
using System.Collections.Generic;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 底部图表面板 - 显示各种数据图表
    /// </summary>
    public class BottomChartPanel : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IController
    {
        [Header("UI组件")]
        [SerializeField] private Transform chartContainer;
        [SerializeField] private GameObject lineChartPrefab;
        [SerializeField] private GameObject barChartPrefab;
        [SerializeField] private GameObject pieChartPrefab;
        [SerializeField] private TextMeshProUGUI titleText;
        [SerializeField] private Dropdown chartTypeDropdown;
        [SerializeField] private But<PERSON> fullscreenButton;
        
        [Header("图表选项卡")]
        [SerializeField] private Toggle powerChartToggle;
        [SerializeField] private Toggle efficiencyChartToggle;
        [SerializeField] private Toggle consumptionChartToggle;
        [SerializeField] private Toggle comparisonChartToggle;
        
        [Header("图表设置")]
        [SerializeField] private Color[] chartColors = new Color[]
        {
            new Color(0.2f, 0.8f, 0.4f),
            new Color(0.8f, 0.4f, 0.2f),
            new Color(0.4f, 0.6f, 1f),
            new Color(1f, 0.8f, 0.2f),
            new Color(0.6f, 0.3f, 0.8f)
        };
        
        private List<ChartComponent> activeCharts = new List<ChartComponent>();
        private IEnergyDashboardModel mModel;
        private ChartType currentChartType = ChartType.Power;
        
        public void Init()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 设置标题
            if (titleText != null)
            {
                titleText.text = "数据分析";
            }
            
            // 绑定图表类型切换
            SetupChartTypeToggles();
            
            // 绑定下拉菜单
            SetupChartTypeDropdown();
            
            // 绑定全屏按钮
            if (fullscreenButton != null)
            {
                fullscreenButton.onClick.AddListener(OnFullscreenClicked);
            }
            
            // 创建默认图表
            CreateDefaultCharts();
            
            // 绑定数据
            BindData();
        }
        
        private void SetupChartTypeToggles()
        {
            if (powerChartToggle != null)
            {
                powerChartToggle.onValueChanged.AddListener(isOn => {
                    if (isOn) SwitchChartType(ChartType.Power);
                });
            }
            
            if (efficiencyChartToggle != null)
            {
                efficiencyChartToggle.onValueChanged.AddListener(isOn => {
                    if (isOn) SwitchChartType(ChartType.Efficiency);
                });
            }
            
            if (consumptionChartToggle != null)
            {
                consumptionChartToggle.onValueChanged.AddListener(isOn => {
                    if (isOn) SwitchChartType(ChartType.Consumption);
                });
            }
            
            if (comparisonChartToggle != null)
            {
                comparisonChartToggle.onValueChanged.AddListener(isOn => {
                    if (isOn) SwitchChartType(ChartType.Comparison);
                });
            }
        }
        
        private void SetupChartTypeDropdown()
        {
            if (chartTypeDropdown != null)
            {
                chartTypeDropdown.options.Clear();
                chartTypeDropdown.options.Add(new Dropdown.OptionData("功率趋势"));
                chartTypeDropdown.options.Add(new Dropdown.OptionData("效率分析"));
                chartTypeDropdown.options.Add(new Dropdown.OptionData("能耗统计"));
                chartTypeDropdown.options.Add(new Dropdown.OptionData("对比分析"));
                
                chartTypeDropdown.onValueChanged.AddListener(OnDropdownValueChanged);
            }
        }
        
        private void OnDropdownValueChanged(int index)
        {
            ChartType chartType = (ChartType)index;
            SwitchChartType(chartType);
        }
        
        private void SwitchChartType(ChartType chartType)
        {
            if (currentChartType == chartType) return;
            
            currentChartType = chartType;
            
            // 更新切换按钮状态
            UpdateToggleStates();
            
            // 重新创建图表
            RecreateCharts();
        }
        
        private void UpdateToggleStates()
        {
            if (powerChartToggle != null)
                powerChartToggle.isOn = currentChartType == ChartType.Power;
            if (efficiencyChartToggle != null)
                efficiencyChartToggle.isOn = currentChartType == ChartType.Efficiency;
            if (consumptionChartToggle != null)
                consumptionChartToggle.isOn = currentChartType == ChartType.Consumption;
            if (comparisonChartToggle != null)
                comparisonChartToggle.isOn = currentChartType == ChartType.Comparison;
            
            if (chartTypeDropdown != null)
                chartTypeDropdown.value = (int)currentChartType;
        }
        
        private void CreateDefaultCharts()
        {
            // 创建功率趋势图表
            CreateChart(ChartType.Power);
        }
        
        private void RecreateCharts()
        {
            // 清理现有图表
            ClearCharts();
            
            // 创建新图表
            CreateChart(currentChartType);
            
            // 播放切换动画
            PlayChartSwitchAnimation();
        }
        
        private void ClearCharts()
        {
            foreach (var chart in activeCharts)
            {
                if (chart != null && chart.gameObject != null)
                {
                    Destroy(chart.gameObject);
                }
            }
            activeCharts.Clear();
        }
        
        private void CreateChart(ChartType chartType)
        {
            if (chartContainer == null) return;
            
            GameObject chartPrefab = GetChartPrefab(chartType);
            if (chartPrefab == null) return;
            
            var chartObj = Instantiate(chartPrefab, chartContainer);
            var chartComponent = chartObj.GetComponent<ChartComponent>();
            
            if (chartComponent == null)
            {
                chartComponent = chartObj.AddComponent<ChartComponent>();
            }
            
            chartComponent.Initialize(chartType, chartColors);
            activeCharts.Add(chartComponent);
        }
        
        private GameObject GetChartPrefab(ChartType chartType)
        {
            switch (chartType)
            {
                case ChartType.Power:
                case ChartType.Efficiency:
                    return lineChartPrefab;
                case ChartType.Consumption:
                    return barChartPrefab;
                case ChartType.Comparison:
                    return pieChartPrefab;
                default:
                    return lineChartPrefab;
            }
        }
        
        public void UpdateChartData(List<ChartData> chartDataList)
        {
            if (chartDataList == null || activeCharts == null) return;
            
            foreach (var chart in activeCharts)
            {
                if (chart != null)
                {
                    chart.UpdateData(chartDataList);
                }
            }
        }
        
        private void PlayChartSwitchAnimation()
        {
            foreach (var chart in activeCharts)
            {
                if (chart != null)
                {
                    chart.transform.localScale = Vector3.zero;
                    chart.transform.DOScale(Vector3.one, 0.5f)
                        .SetEase(Ease.OutBack);
                }
            }
        }
        
        private void OnFullscreenClicked()
        {
            // 发送全屏显示命令
            this.SendCommand(new ShowFullscreenChartCommand(currentChartType));
            
            // 播放全屏动画
            if (fullscreenButton != null)
            {
                fullscreenButton.transform.DOPunchScale(Vector3.one * 0.1f, 0.2f, 1, 0.5f);
            }
        }
        
        private void BindData()
        {
            mModel.ChartDataList.RegisterWithInitValue(UpdateChartData)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
        
        private void OnDestroy()
        {
            if (fullscreenButton != null)
                fullscreenButton.onClick.RemoveListener(OnFullscreenClicked);
            
            if (chartTypeDropdown != null)
                chartTypeDropdown.onValueChanged.RemoveListener(OnDropdownValueChanged);
        }
    }
    
    public enum ChartType
    {
        Power = 0,
        Efficiency = 1,
        Consumption = 2,
        Comparison = 3
    }
}
