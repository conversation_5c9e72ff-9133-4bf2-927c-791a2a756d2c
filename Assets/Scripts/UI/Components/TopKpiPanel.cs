using UnityEngine;
using UnityEngine.UI;
using QFramework;
using TMPro;
using System.Collections.Generic;
using DG.Tweening;

namespace EnergyDashboard.UI
{
    /// <summary>
    /// 顶部KPI面板 - 显示关键指标
    /// </summary>
    public class TopKpiPanel : Mono<PERSON>ehaviour, IController
    {
        [Header("UI组件")]
        [SerializeField] private Transform kpiContainer;
        [SerializeField] private GameObject kpiItemPrefab;
        [SerializeField] private TextMeshProUGUI timeRangeText;
        [SerializeField] private Button refreshButton;
        
        [Header("动画设置")]
        [SerializeField] private float animationDuration = 0.5f;
        [SerializeField] private Ease animationEase = Ease.OutQuart;
        
        private List<KpiItem> kpiItems = new List<KpiItem>();
        private IEnergyDashboardModel mModel;
        
        public void Init()
        {
            mModel = this.GetModel<IEnergyDashboardModel>();
            
            // 绑定刷新按钮
            if (refreshButton != null)
            {
                refreshButton.onClick.AddListener(OnRefreshClicked);
            }
            
            // 创建KPI项
            CreateKpiItems();
            
            // 绑定数据
            BindData();
        }
        
        private void CreateKpiItems()
        {
            if (kpiItemPrefab == null || kpiContainer == null) return;
            
            // 定义KPI项配置
            var kpiConfigs = new[]
            {
                new KpiConfig { Title = "总发电量", Unit = "kWh", Icon = "power_icon", Color = new Color(0.2f, 0.8f, 0.4f) },
                new KpiConfig { Title = "总用电量", Unit = "kWh", Icon = "consumption_icon", Color = new Color(0.8f, 0.4f, 0.2f) },
                new KpiConfig { Title = "储能状态", Unit = "%", Icon = "battery_icon", Color = new Color(0.4f, 0.6f, 1f) },
                new KpiConfig { Title = "设备效率", Unit = "%", Icon = "efficiency_icon", Color = new Color(1f, 0.8f, 0.2f) },
                new KpiConfig { Title = "碳排放", Unit = "kg", Icon = "carbon_icon", Color = new Color(0.6f, 0.3f, 0.8f) },
                new KpiConfig { Title = "成本节约", Unit = "¥", Icon = "money_icon", Color = new Color(0.2f, 0.9f, 0.6f) }
            };
            
            foreach (var config in kpiConfigs)
            {
                CreateKpiItem(config);
            }
        }
        
        private void CreateKpiItem(KpiConfig config)
        {
            var itemObj = Instantiate(kpiItemPrefab, kpiContainer);
            var kpiItem = itemObj.GetComponent<KpiItem>();
            
            if (kpiItem == null)
            {
                kpiItem = itemObj.AddComponent<KpiItem>();
            }
            
            kpiItem.Initialize(config);
            kpiItems.Add(kpiItem);
        }
        
        public void UpdateKpiData(List<KpiData> kpiDataList)
        {
            if (kpiDataList == null || kpiItems == null) return;
            
            for (int i = 0; i < kpiItems.Count && i < kpiDataList.Count; i++)
            {
                var kpiItem = kpiItems[i];
                var kpiData = kpiDataList[i];
                
                // 使用动画更新数值
                kpiItem.UpdateValue(kpiData.Value, kpiData.Change, animationDuration);
            }
        }
        
        public void UpdateTimeRange(TimeRange timeRange)
        {
            if (timeRangeText != null)
            {
                timeRangeText.text = GetTimeRangeDisplayText(timeRange);
            }
        }
        
        private string GetTimeRangeDisplayText(TimeRange timeRange)
        {
            switch (timeRange.Type)
            {
                case TimeRangeType.Today:
                    return "今日数据";
                case TimeRangeType.Yesterday:
                    return "昨日数据";
                case TimeRangeType.ThisWeek:
                    return "本周数据";
                case TimeRangeType.ThisMonth:
                    return "本月数据";
                case TimeRangeType.Custom:
                    return $"{timeRange.StartTime:MM/dd} - {timeRange.EndTime:MM/dd}";
                default:
                    return "数据概览";
            }
        }
        
        private void OnRefreshClicked()
        {
            // 播放刷新动画
            if (refreshButton != null)
            {
                refreshButton.transform.DORotate(new Vector3(0, 0, 360), 1f, RotateMode.FastBeyond360)
                    .SetEase(Ease.OutQuart);
            }
            
            // 发送刷新命令
            this.SendCommand<RefreshDataCommand>();
        }
        
        private void BindData()
        {
            mModel.KpiDataList.RegisterWithInitValue(UpdateKpiData)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
            
            mModel.SelectedTimeRange.RegisterWithInitValue(UpdateTimeRange)
                .UnRegisterWhenGameObjectDestroyed(gameObject);
        }
        
        // 添加入场动画
        public void PlayEnterAnimation()
        {
            for (int i = 0; i < kpiItems.Count; i++)
            {
                var item = kpiItems[i];
                item.transform.localScale = Vector3.zero;
                item.transform.DOScale(Vector3.one, animationDuration)
                    .SetDelay(i * 0.1f)
                    .SetEase(animationEase);
            }
        }
        
        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }
        
        private void OnDestroy()
        {
            if (refreshButton != null)
            {
                refreshButton.onClick.RemoveListener(OnRefreshClicked);
            }
        }
    }
    
    [System.Serializable]
    public struct KpiConfig
    {
        public string Title;
        public string Unit;
        public string Icon;
        public Color Color;
    }
}
