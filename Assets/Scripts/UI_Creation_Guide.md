# 能源看板UI创建指南

## 📋 UI结构总览

基于上面的结构图，您需要在Unity中创建以下UI层级结构：

## 🏗️ 创建步骤

### 1. 创建Canvas
```
Canvas (Screen Space - Overlay)
├── Canvas Scaler (UI Scale Mode: Scale With Screen Size)
└── Graphic Raycaster
```

### 2. 创建主面板
```
EnergyDashboardPanel (GameObject + EnergyDashboardPanel.cs)
├── Background (Image - 半透明背景)
└── Content (Vertical Layout Group)
```

### 3. 创建Header区域
```
Header (Horizontal Layout Group)
├── TitleText (Text: "能源数据看板")
├── Spacer (Layout Element - Flexible Width)
├── TimeText (Text: "2024-08-04 20:30")
└── RefreshButton (Button + Text: "刷新")
```

### 4. 创建KPI卡片区域
```
KPIContainer (Horizontal Layout Group)
├── PowerCard (Image + Vertical Layout Group)
│   ├── PowerIcon (Image)
│   ├── PowerValue (Text: "1250.5 kW") ← 绑定到totalPowerValue
│   └── PowerTrend (Text: "↑5.2%") ← 绑定到totalPowerTrend
├── ConsumptionCard (Image + Vertical Layout Group)
│   ├── ConsumptionIcon (Image)
│   ├── ConsumptionValue (Text: "8760.3 kWh") ← 绑定到totalConsumptionValue
│   └── ConsumptionTrend (Text: "↓2.1%") ← 绑定到totalConsumptionTrend
├── EfficiencyCard (Image + Vertical Layout Group)
│   ├── EfficiencyIcon (Image)
│   ├── EfficiencyValue (Text: "87.6%") ← 绑定到efficiencyValue
│   └── EfficiencyTrend (Text: "↑1.8%") ← 绑定到efficiencyTrend
└── CarbonCard (Image + Vertical Layout Group)
    ├── CarbonIcon (Image)
    ├── CarbonValue (Text: "245.7 kg") ← 绑定到carbonEmissionValue
    └── CarbonTrend (Text: "↓8.5%") ← 绑定到carbonEmissionTrend
```

### 5. 创建图表区域
```
ChartsContainer (Grid Layout Group - 2x2)
├── PowerChart (BarChart) ← 绑定到powerChart
├── ConsumptionChart (LineChart) ← 绑定到consumptionChart
├── EfficiencyChart (PieChart) ← 绑定到efficiencyChart
└── GaugeChart (RingChart) ← 绑定到gaugeChart
```

### 6. 创建控制区域
```
ControlsContainer (Horizontal Layout Group)
├── TimeRangeGroup (Horizontal Layout Group)
│   ├── TodayButton (Button + Text: "今天") ← 绑定到todayButton
│   ├── WeekButton (Button + Text: "本周") ← 绑定到weekButton
│   └── MonthButton (Button + Text: "本月") ← 绑定到monthButton
└── FilterGroup (Vertical Layout Group)
    ├── FilterTitle (Text: "筛选条件")
    └── FilterItemParent (Vertical Layout Group) ← 绑定到filterItemParent
```

### 7. 创建筛选项预制体
```
FilterItemPrefab (Horizontal Layout Group) ← 绑定到filterItemPrefab
├── ItemToggle (Toggle)
├── ItemIcon (Image)
└── ItemText (Text)
```

### 8. 创建加载面板
```
LoadingPanel (GameObject + Canvas Group) ← 绑定到loadingPanel
├── LoadingBackground (Image - 半透明黑色)
├── LoadingSpinner (Image + Rotation Animation)
└── LoadingText (Text: "数据加载中...")
```

## 🎨 样式建议

### KPI卡片样式
- **背景**: 圆角矩形，渐变色
- **总功率**: 蓝色主题 (#2196F3)
- **总消耗**: 紫色主题 (#9C27B0)
- **效率**: 绿色主题 (#4CAF50)
- **碳排放**: 橙色主题 (#FF9800)

### 图表样式
- **统一尺寸**: 每个图表相同大小
- **圆角边框**: 2-4px圆角
- **阴影效果**: 轻微阴影增加层次感

### 按钮样式
- **主要按钮**: 蓝色背景，白色文字
- **次要按钮**: 白色背景，蓝色边框
- **悬停效果**: 颜色加深

## 📱 响应式设计

### 布局适配
- 使用Layout Group组件自动排列
- 设置Content Size Fitter自适应内容
- 使用Flexible Width/Height适应屏幕

### 分辨率适配
- Canvas Scaler设置为Scale With Screen Size
- 参考分辨率: 1920x1080
- Match: 0.5 (宽高平衡)

## 🔧 组件绑定清单

### 必须绑定的SerializeField
- [ ] totalPowerValue (Text)
- [ ] totalPowerTrend (Text)
- [ ] totalConsumptionValue (Text)
- [ ] totalConsumptionTrend (Text)
- [ ] efficiencyValue (Text)
- [ ] efficiencyTrend (Text)
- [ ] carbonEmissionValue (Text)
- [ ] carbonEmissionTrend (Text)
- [ ] powerChart (BarChart)
- [ ] consumptionChart (LineChart)
- [ ] efficiencyChart (PieChart)
- [ ] gaugeChart (RingChart)
- [ ] refreshButton (Button)
- [ ] todayButton (Button)
- [ ] weekButton (Button)
- [ ] monthButton (Button)
- [ ] filterItemParent (Transform)
- [ ] filterItemPrefab (GameObject)
- [ ] loadingPanel (GameObject)

## 🚀 快速创建脚本

可以使用以下MenuItem脚本快速创建UI结构：

```csharp
[MenuItem("EnergyDashboard/Create UI Structure")]
public static void CreateUIStructure()
{
    // 创建Canvas
    var canvas = new GameObject("Canvas").AddComponent<Canvas>();
    canvas.renderMode = RenderMode.ScreenSpaceOverlay;
    
    // 创建主面板
    var panel = new GameObject("EnergyDashboardPanel");
    panel.transform.SetParent(canvas.transform);
    panel.AddComponent<EnergyDashboardPanel>();
    
    // ... 继续创建其他UI元素
}
```

## 📝 注意事项

1. **XCharts组件**: 确保从XCharts菜单创建图表组件
2. **预制体路径**: 将FilterItemPrefab保存为预制体
3. **事件绑定**: 按钮事件会在代码中自动绑定
4. **动态内容**: 筛选项会在运行时动态创建
5. **性能优化**: 大量筛选项时考虑使用ScrollView
