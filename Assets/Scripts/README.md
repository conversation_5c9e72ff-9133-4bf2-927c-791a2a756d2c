# 能源数据看板系统 (Energy Dashboard System)

基于QFramework架构和XCharts插件的能源数据可视化看板MVP实现。

## 快速开始

### 1. 场景设置
1. 在Unity场景中创建一个空GameObject，命名为"EnergyDashboardTest"
2. 添加`EnergyDashboardTest`脚本
3. 创建UI Canvas和EnergyDashboardPanel预制体
4. 将EnergyDashboardPanel预制体拖拽到EnergyDashboardTest的dashboardPanel字段
5. 运行场景

### 2. 预制体设置
创建EnergyDashboardPanel预制体，包含以下UI组件：

#### 必需的UI组件
- **KPI区域**: 8个Text组件用于显示KPI数值和趋势
- **图表区域**: 4个XCharts组件（BarChart, LineChart, PieChart, GaugeChart）
- **控制区域**: 4个Button组件和筛选项容器
- **加载状态**: 1个GameObject作为加载面板

#### 可选的UI组件
- 筛选项预制体：包含Text和Toggle组件

### 3. 测试功能
- 按T键切换面板显示/隐藏
- 使用Inspector中的Context Menu测试各种功能
- 查看Console输出了解系统状态

## 系统架构

### QFramework MVC架构
- **Model**: `EnergyDashboardModel` - 数据模型
- **View**: `EnergyDashboardPanel` - UI视图
- **Controller**: 通过Command模式处理用户交互
- **System**: `EnergyDataSystem` - 业务逻辑处理

### 主要组件

#### 1. EnergyDashboardArchitecture
- 架构注册中心
- 负责注册Model和System

#### 2. EnergyDashboardModel
- 存储所有看板数据
- 提供数据绑定属性
- 生成模拟数据

#### 3. EnergyDataSystem
- 处理数据业务逻辑
- 根据筛选条件生成数据
- 模拟异步数据加载

#### 4. Commands
- `InitializeEnergyDataCommand`: 初始化数据
- `RefreshEnergyDataCommand`: 刷新数据
- `UpdateFilterSelectionCommand`: 更新筛选
- `UpdateTimeRangeCommand`: 更新时间范围

#### 5. EnergyDashboardPanel
- UI视图控制器
- 处理用户交互
- 更新UI显示

## 功能特性

### KPI指标显示
- 总功率 (kW)
- 总消耗 (kWh)
- 效率 (%)
- 碳排放 (kg)
- 趋势指示（上升/下降）

### 图表可视化
- 柱状图：功率时间序列
- 折线图：消耗时间序列
- 饼图：效率分布
- 仪表盘：当前效率

### 交互功能
- 时间范围选择（今天/本周/本月）
- 设备/区域筛选
- 数据刷新
- 加载状态显示

## 数据流程

1. **初始化**: EnergyDashboardTest初始化架构
2. **面板激活**: EnergyDashboardPanel.OnInit()被调用
3. **数据加载**: 发送InitializeEnergyDataCommand
4. **模型更新**: EnergyDashboardModel.LoadMockData()生成数据
5. **事件通知**: 发送EnergyDataUpdatedEvent
6. **UI更新**: Panel监听事件，更新所有UI组件

## 扩展指南

### 添加新的KPI指标
1. 在`KpiData`类中添加新属性
2. 在`EnergyDashboardModel.LoadMockData()`中生成数据
3. 在`EnergyDashboardPanel.UpdateKpiUI()`中添加UI更新逻辑

### 添加新的图表类型
1. 在Panel中添加XCharts组件引用
2. 在`UpdateChartsUI()`方法中添加图表更新逻辑

### 连接真实数据源
1. 替换`EnergyDashboardModel.LoadMockData()`中的模拟数据
2. 在`EnergyDataSystem`中添加API调用逻辑
3. 添加错误处理和重试机制

## 注意事项

1. **依赖项**: 确保项目中已安装QFramework和XCharts插件
2. **性能**: 大量数据时考虑分页或数据虚拟化
3. **错误处理**: 添加网络错误和数据异常处理
4. **UI适配**: 考虑不同分辨率的UI适配

## 故障排除

### 常见问题
1. **架构未初始化**: 确保EnergyDashboardTest脚本在场景中运行
2. **UI组件未绑定**: 检查EnergyDashboardPanel中的SerializeField引用
3. **图表不显示**: 确保XCharts组件正确配置
4. **事件不响应**: 检查QFramework事件系统是否正常工作

### 调试技巧
1. 使用Context Menu测试各种功能
2. 查看Console输出了解系统状态
3. 使用Unity Profiler监控性能
4. 检查QFramework架构是否正确注册

## 版本信息
- Unity版本: 2022.3+
- QFramework版本: 最新版本
- XCharts版本: 3.0+
