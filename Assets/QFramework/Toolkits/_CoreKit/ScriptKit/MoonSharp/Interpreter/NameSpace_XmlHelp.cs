
namespace MoonSharp.Interpreter 
{
	/// <summary>
	/// Namespace containing all the most commonly used classes for MoonSharp operation.
	/// When in doubt, refer to <see cref="<PERSON>ript" /> and <see cref="DynValue" /> classes as starting points.
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.CoreLib
{
	/// <summary>
	/// Namespace containing the implementation of the Lua standard library, as MoonSharp modules.
	/// There's seldom the need to access these classes directly.
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.CoreLib.IO
{
	/// <summary>
	/// Namespace containing userdata classes for the 'io' module
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.Debugging
{
	/// <summary>
	/// Namespace containing classes used to support debuggers
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.Diagnostics
{
	/// <summary>
	/// Namespace containing classes used to support self diagnostics (e.g. performance counters)
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.Interop
{
	/// <summary>
	/// Namespace containing classes used to customize and support advanced interoperations between
	/// scripts and CLR code.
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.Interop.LuaStateInterop
{
	/// <summary>
	/// Namespace containing classes used to provide a minimal support for porting code based on the classic
	/// "LuaState" model (e.g. code based on KopiLua, UniLua or Lua itself).
	/// </summary>
	internal static class NamespaceDoc { }
}


namespace MoonSharp.Interpreter.Loaders
{
	/// <summary>
	/// Namespace containing classes used to customized how scripts are loaded from external files.
	/// </summary>
	internal static class NamespaceDoc { }
}


namespace MoonSharp.Interpreter.Platforms
{
	/// <summary>
	/// Namespace containing classes used to customize how the interfacing with the operative system happens
	/// for the 'os' and 'io' modules, and for future modules/classes which may require OS access.
	/// See <see cref="IPlatformAccessor"/> and <see cref="ScriptGlobalOptions.Platform"/> .
	/// </summary>
	internal static class NamespaceDoc { }
}

namespace MoonSharp.Interpreter.REPL
{
	/// <summary>
	/// Contains classes useful to implement REPL interpreters.
	/// See : <see href="http://en.wikipedia.org/wiki/Read%E2%80%93eval%E2%80%93print_loop" />
	/// </summary>
	internal static class NamespaceDoc { }
}






