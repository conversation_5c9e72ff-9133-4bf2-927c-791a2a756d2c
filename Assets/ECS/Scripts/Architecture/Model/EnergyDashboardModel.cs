using System;
using System.Collections.Generic;
using QFramework;
using UnityEngine;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源看板数据模型 - 唯一可信的数据源
    /// </summary>
    public interface IEnergyDashboardModel : IModel
    {
        BindableProperty<EnergyFilterData> FilterData { get; }
        BindableProperty<KpiData> KpiData { get; }
        BindableProperty<TimeSeriesChartData> ChartData { get; }
        BindableProperty<List<SimpleFilterItem>> FilterItems { get; }
        BindableProperty<bool> IsLoading { get; }
        
        void LoadMockData();
    }

    public class EnergyDashboardModel : AbstractModel, IEnergyDashboardModel
    {
        public BindableProperty<EnergyFilterData> FilterData { get; } = new BindableProperty<EnergyFilterData>();
        public BindableProperty<KpiData> KpiData { get; } = new BindableProperty<KpiData>();
        public BindableProperty<TimeSeriesChartData> ChartData { get; } = new BindableProperty<TimeSeriesChartData>();
        public BindableProperty<List<SimpleFilterItem>> FilterItems { get; } = new BindableProperty<List<SimpleFilterItem>>();
        public BindableProperty<bool> IsLoading { get; } = new BindableProperty<bool>(false);

        protected override void OnInit()
        {
            // 初始化默认筛选条件
            FilterData.Value = new EnergyFilterData
            {
                StartTime = DateTime.Now.Date.AddDays(-7),
                EndTime = DateTime.Now.Date,
                SelectedObjectIds = new List<string>()
            };
        }

        public void LoadMockData()
        {
            // 生成筛选项数据
            var filterItems = new List<SimpleFilterItem>
            {
                new SimpleFilterItem { Id = "area_1", Name = "A01-生产区域", Type = FilterItemType.Area, IsSelected = true },
                new SimpleFilterItem { Id = "area_2", Name = "A02-仓储区域", Type = FilterItemType.Area, IsSelected = false },
                new SimpleFilterItem { Id = "area_3", Name = "A03-办公区域", Type = FilterItemType.Area, IsSelected = false },
                new SimpleFilterItem { Id = "device_1", Name = "D01-生产设备", Type = FilterItemType.Device, IsSelected = true },
                new SimpleFilterItem { Id = "device_2", Name = "D01-空调系统", Type = FilterItemType.Device, IsSelected = false },
                new SimpleFilterItem { Id = "device_3", Name = "D01-照明系统", Type = FilterItemType.Device, IsSelected = false },
                new SimpleFilterItem { Id = "device_4", Name = "D01-质检设备", Type = FilterItemType.Device, IsSelected = false }
            };
            FilterItems.Value = filterItems;

            // 生成KPI数据
            var kpiData = new KpiData
            {
                TotalPower = new KpiItem { Value = 1250.5f, Trend = 5.2f, Unit = "kW" },
                TotalConsumption = new KpiItem { Value = 8760.3f, Trend = -2.1f, Unit = "kWh" },
                Efficiency = new KpiItem { Value = 87.6f, Trend = 1.8f, Unit = "%" },
                CarbonEmission = new KpiItem { Value = 245.7f, Trend = -8.5f, Unit = "kg" }
            };
            KpiData.Value = kpiData;

            // 生成时间序列图表数据
            var chartData = new TimeSeriesChartData
            {
                Categories = GenerateTimeCategories(),
                PowerSeries = GeneratePowerSeries(),
                ConsumptionSeries = GenerateConsumptionSeries(),
                EfficiencySeries = GenerateEfficiencySeries()
            };
            ChartData.Value = chartData;

            // 发送数据更新事件
            this.SendEvent<EnergyDataUpdatedEvent>();
        }

        private List<string> GenerateTimeCategories()
        {
            var categories = new List<string>();
            var startTime = FilterData.Value.StartTime;
            var endTime = FilterData.Value.EndTime;
            var timeSpan = endTime - startTime;
            var intervalHours = Math.Max(1, (int)(timeSpan.TotalHours / 24)); // 最多24个数据点

            for (var time = startTime; time <= endTime; time = time.AddHours(intervalHours))
            {
                categories.Add(time.ToString("MM/dd HH:mm"));
            }

            return categories;
        }

        private List<float> GeneratePowerSeries()
        {
            var series = new List<float>();
            var random = new System.Random();
            var baseValue = 1000f;
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-200, 300);
                series.Add(value);
            }
            
            return series;
        }

        private List<float> GenerateConsumptionSeries()
        {
            var series = new List<float>();
            var random = new System.Random();
            var baseValue = 800f;
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-150, 200);
                series.Add(value);
            }
            
            return series;
        }

        private List<float> GenerateEfficiencySeries()
        {
            var series = new List<float>();
            var random = new System.Random();
            var baseValue = 85f;
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-10, 15);
                value = Mathf.Clamp(value, 0f, 100f);
                series.Add(value);
            }
            
            return series;
        }
    }

    // 数据结构定义
    [Serializable]
    public class EnergyFilterData
    {
        public DateTime StartTime;
        public DateTime EndTime;
        public List<string> SelectedObjectIds = new List<string>();
    }

    [Serializable]
    public class KpiData
    {
        public KpiItem TotalPower;
        public KpiItem TotalConsumption;
        public KpiItem Efficiency;
        public KpiItem CarbonEmission;
    }

    [Serializable]
    public class KpiItem
    {
        public float Value;
        public float Trend; // 趋势百分比，正数为上升，负数为下降
        public string Unit;
    }

    [Serializable]
    public class TimeSeriesChartData
    {
        public List<string> Categories = new List<string>();
        public List<float> PowerSeries = new List<float>();
        public List<float> ConsumptionSeries = new List<float>();
        public List<float> EfficiencySeries = new List<float>();
    }

    [Serializable]
    public class SimpleFilterItem
    {
        public string Id;
        public string Name;
        public FilterItemType Type;
        public bool IsSelected;
    }

    public enum FilterItemType
    {
        Area,
        Device
    }

    // 事件定义
    public struct EnergyDataUpdatedEvent
    {
    }
}
