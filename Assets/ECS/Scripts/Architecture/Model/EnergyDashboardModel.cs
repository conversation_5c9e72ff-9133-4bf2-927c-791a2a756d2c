using System;
using System.Collections.Generic;
using QFramework;
using UnityEngine;

namespace EnergyDashboard
{
    /// <summary>
    /// 能源看板数据模型 - 唯一可信的数据源
    /// </summary>
    public interface IEnergyDashboardModel : IModel
    {
        BindableProperty<EnergyFilterData> FilterData { get; }
        BindableProperty<KpiData> KpiData { get; }
        BindableProperty<TimeSeriesChartData> ChartData { get; }
        BindableProperty<List<SimpleFilterItem>> FilterItems { get; }
        BindableProperty<bool> IsLoading { get; }
        
        void LoadMockData();
    }

    public class EnergyDashboardModel : AbstractModel, IEnergyDashboardModel
    {
        public BindableProperty<EnergyFilterData> FilterData { get; } = new BindableProperty<EnergyFilterData>();
        public BindableProperty<KpiData> KpiData { get; } = new BindableProperty<KpiData>();
        public BindableProperty<TimeSeriesChartData> ChartData { get; } = new BindableProperty<TimeSeriesChartData>();
        public BindableProperty<List<SimpleFilterItem>> FilterItems { get; } = new BindableProperty<List<SimpleFilterItem>>();
        public BindableProperty<bool> IsLoading { get; } = new BindableProperty<bool>(false);

        protected override void OnInit()
        {
            // 初始化默认筛选条件
            FilterData.Value = new EnergyFilterData
            {
                StartTime = DateTime.Now.Date.AddDays(-7),
                EndTime = DateTime.Now.Date,
                SelectedObjectIds = new List<string>()
            };
        }

        public void LoadMockData()
        {
            // 生成筛选项数据
            var filterItems = new List<SimpleFilterItem>
            {
                new SimpleFilterItem { Id = "area_1", Name = "A01-生产区域", Type = FilterItemType.Area, IsSelected = true },
                new SimpleFilterItem { Id = "area_2", Name = "A02-仓储区域", Type = FilterItemType.Area, IsSelected = false },
                new SimpleFilterItem { Id = "area_3", Name = "A03-办公区域", Type = FilterItemType.Area, IsSelected = false },
                new SimpleFilterItem { Id = "device_1", Name = "D01-生产设备", Type = FilterItemType.Device, IsSelected = true },
                new SimpleFilterItem { Id = "device_2", Name = "D01-空调系统", Type = FilterItemType.Device, IsSelected = false },
                new SimpleFilterItem { Id = "device_3", Name = "D01-照明系统", Type = FilterItemType.Device, IsSelected = false },
                new SimpleFilterItem { Id = "device_4", Name = "D01-质检设备", Type = FilterItemType.Device, IsSelected = false }
            };
            FilterItems.Value = filterItems;

            // 生成8个KPI模块数据
            var kpiData = new KpiData
            {
                // 上排数值型KPI模块
                TotalConsumption = new NumericKpiItem
                {
                    Value = 8760.3f,
                    Unit = "kWh",
                    Trend = -2.1f,
                    AuxiliaryInfo = "365.0",
                    AuxiliaryLabel = "平均日耗"
                },
                EstimatedCost = new NumericKpiItem
                {
                    Value = 6132.2f,
                    Unit = "元",
                    Trend = 3.5f,
                    AuxiliaryInfo = "0.70",
                    AuxiliaryLabel = "单价"
                },
                CarbonEmission = new NumericKpiItem
                {
                    Value = 5486.2f,
                    Unit = "kg",
                    Trend = -8.5f,
                    AuxiliaryInfo = "0.626",
                    AuxiliaryLabel = "系数"
                },
                PowerFactor = new NumericKpiItem
                {
                    Value = 0.92f,
                    Unit = "",
                    Trend = 1.8f,
                    AuxiliaryInfo = "0.95",
                    AuxiliaryLabel = "目标"
                },

                // 下排状态型KPI模块
                LoadRate = new StatusKpiItem
                {
                    MainValue = 78.5f,
                    MainUnit = "%",
                    SecondaryValue = 1570.0f,
                    SecondaryUnit = "kW",
                    TertiaryValue = 2000.0f,
                    TertiaryUnit = "kW",
                    Status = KpiStatus.Normal,
                    StatusText = "正常",
                    StatusColor = Color.green
                },
                DeviceStatus = new StatusKpiItem
                {
                    MainValue = 45.0f,
                    MainUnit = "台",
                    SecondaryValue = 3.0f,
                    SecondaryUnit = "台",
                    TertiaryValue = 1.0f,
                    TertiaryUnit = "台",
                    Status = KpiStatus.Attention,
                    StatusText = "注意",
                    StatusColor = Color.yellow
                },
                EfficiencyGrade = new StatusKpiItem
                {
                    MainValue = 65.0f,
                    MainUnit = "%",
                    SecondaryValue = 25.0f,
                    SecondaryUnit = "%",
                    TertiaryValue = 10.0f,
                    TertiaryUnit = "%",
                    Status = KpiStatus.Excellent,
                    StatusText = "优秀",
                    StatusColor = Color.blue
                },
                EnergySaving = new StatusKpiItem
                {
                    MainValue = 85.2f,
                    MainUnit = "%",
                    SecondaryValue = 1278.5f,
                    SecondaryUnit = "kWh",
                    TertiaryValue = 1500.0f,
                    TertiaryUnit = "kWh",
                    Status = KpiStatus.Target,
                    StatusText = "达标",
                    StatusColor = Color.cyan
                }
            };
            KpiData.Value = kpiData;

            // 生成时间序列图表数据
            var chartData = new TimeSeriesChartData
            {
                Categories = GenerateTimeCategories(),
                PowerSeries = GeneratePowerSeries(),
                ConsumptionSeries = GenerateConsumptionSeries(),
                EfficiencySeries = GenerateEfficiencySeries()
            };
            ChartData.Value = chartData;

            // 发送数据更新事件
            this.SendEvent<EnergyDataUpdatedEvent>();
        }

        private List<string> GenerateTimeCategories()
        {
            var categories = new List<string>();
            var startTime = FilterData.Value.StartTime;
            var endTime = FilterData.Value.EndTime;
            var timeSpan = endTime - startTime;
            var intervalHours = Math.Max(1, (int)(timeSpan.TotalHours / 24)); // 最多24个数据点

            for (var time = startTime; time <= endTime; time = time.AddHours(intervalHours))
            {
                categories.Add(time.ToString("MM/dd HH:mm"));
            }

            return categories;
        }

        private List<float> GeneratePowerSeries()
        {
            var series = new List<float>();
            var random = new System.Random();
            var baseValue = 1000f;
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-200, 300);
                series.Add(value);
            }
            
            return series;
        }

        private List<float> GenerateConsumptionSeries()
        {
            var series = new List<float>();
            var random = new System.Random();
            var baseValue = 800f;
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-150, 200);
                series.Add(value);
            }
            
            return series;
        }

        private List<float> GenerateEfficiencySeries()
        {
            var series = new List<float>();
            var random = new System.Random();
            var baseValue = 85f;
            
            for (int i = 0; i < 24; i++)
            {
                var value = baseValue + random.Next(-10, 15);
                value = Mathf.Clamp(value, 0f, 100f);
                series.Add(value);
            }
            
            return series;
        }
    }

    // 数据结构定义
    [Serializable]
    public class EnergyFilterData
    {
        public DateTime StartTime;
        public DateTime EndTime;
        public List<string> SelectedObjectIds = new List<string>();
    }

    [Serializable]
    public class KpiData
    {
        // 上排数值型KPI模块
        public NumericKpiItem TotalConsumption;     // 周期内总消耗
        public NumericKpiItem EstimatedCost;        // 预估电费
        public NumericKpiItem CarbonEmission;       // 碳排放量
        public NumericKpiItem PowerFactor;          // 平均功率因数

        // 下排状态型KPI模块
        public StatusKpiItem LoadRate;              // 实时负荷率
        public StatusKpiItem DeviceStatus;          // 设备运行状态
        public StatusKpiItem EfficiencyGrade;       // 能效等级分布
        public StatusKpiItem EnergySaving;          // 节能目标完成度
    }

    [Serializable]
    public class NumericKpiItem
    {
        public float Value;                         // 主要数值
        public string Unit;                         // 单位
        public float Trend;                         // 趋势百分比，正数为上升，负数为下降
        public string AuxiliaryInfo;                // 辅助信息（平均日耗、单价、系数、目标等）
        public string AuxiliaryLabel;               // 辅助信息标签
    }

    [Serializable]
    public class StatusKpiItem
    {
        public float MainValue;                     // 主要数值
        public string MainUnit;                     // 主要数值单位
        public float SecondaryValue;                // 次要数值
        public string SecondaryUnit;                // 次要数值单位
        public float TertiaryValue;                 // 第三数值
        public string TertiaryUnit;                 // 第三数值单位
        public KpiStatus Status;                    // 状态枚举
        public string StatusText;                   // 状态文本
        public Color StatusColor;                   // 状态颜色
    }

    public enum KpiStatus
    {
        Normal,     // 正常 - 绿色
        Attention,  // 注意 - 黄色
        Excellent,  // 优秀 - 蓝色
        Target      // 达标 - 青色
    }

    [Serializable]
    public class TimeSeriesChartData
    {
        public List<string> Categories = new List<string>();
        public List<float> PowerSeries = new List<float>();
        public List<float> ConsumptionSeries = new List<float>();
        public List<float> EfficiencySeries = new List<float>();
    }

    [Serializable]
    public class SimpleFilterItem
    {
        public string Id;
        public string Name;
        public FilterItemType Type;
        public bool IsSelected;
    }

    public enum FilterItemType
    {
        Area,
        Device
    }

    // 事件定义
    public struct EnergyDataUpdatedEvent
    {
    }
}
