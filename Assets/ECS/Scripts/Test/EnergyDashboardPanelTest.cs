using UnityEngine;
using QFramework;
using EnergyDashboard;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 能源看板面板测试脚本
    /// 用于测试EnergyDashboardPanel的功能
    /// </summary>
    public class EnergyDashboardPanelTest : MonoBehaviour, IController
    {
        [Header("测试设置")]
        [SerializeField] private bool autoOpenPanel = true;
        [SerializeField] private KeyCode testKey = KeyCode.T;

        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }

        void Start()
        {
            // 初始化架构
            EnergyDashboardArchitecture.InitArchitecture();

            Debug.Log("[EnergyDashboardPanelTest] 架构初始化完成");

            if (autoOpenPanel)
            {
                OpenEnergyDashboardPanel();
            }
        }

        void Update()
        {
            if (Input.GetKeyDown(testKey))
            {
                OpenEnergyDashboardPanel();
            }
        }

        [ContextMenu("打开能源看板")]
        public void OpenEnergyDashboardPanel()
        {
            try
            {
                // 使用QFramework的UI系统打开面板
                UIKit.OpenPanel<EnergyDashboardPanel>();
                Debug.Log("[EnergyDashboardPanelTest] 能源看板面板已打开");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardPanelTest] 打开面板时出错: {ex.Message}");
            }
        }

        [ContextMenu("关闭能源看板")]
        public void CloseEnergyDashboardPanel()
        {
            try
            {
                UIKit.ClosePanel<EnergyDashboardPanel>();
                Debug.Log("[EnergyDashboardPanelTest] 能源看板面板已关闭");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardPanelTest] 关闭面板时出错: {ex.Message}");
            }
        }

        [ContextMenu("测试数据刷新")]
        public void TestDataRefresh()
        {
            try
            {
                this.SendCommand<RefreshEnergyDataCommand>();
                Debug.Log("[EnergyDashboardPanelTest] 数据刷新命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardPanelTest] 发送刷新命令时出错: {ex.Message}");
            }
        }

        [ContextMenu("测试筛选更新")]
        public void TestFilterUpdate()
        {
            try
            {
                // 测试选择第一个筛选项
                this.SendCommand(new UpdateFilterSelectionCommand("area_1", true));
                Debug.Log("[EnergyDashboardPanelTest] 筛选更新命令已发送");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardPanelTest] 发送筛选命令时出错: {ex.Message}");
            }
        }

        [ContextMenu("打印架构状态")]
        public void PrintArchitectureStatus()
        {
            try
            {
                var model = this.GetModel<IEnergyDashboardModel>();
                var system = this.GetSystem<IEnergyDataSystem>();

                Debug.Log("[EnergyDashboardPanelTest] 架构状态:");
                Debug.Log($"  - Model已注册: {model != null}");
                Debug.Log($"  - System已注册: {system != null}");
                Debug.Log($"  - 架构已初始化: {EnergyDashboardArchitecture.Interface != null}");

                if (model != null)
                {
                    Debug.Log($"  - KPI数据: {model.KpiData.Value != null}");
                    Debug.Log($"  - 图表数据: {model.ChartData.Value != null}");
                    Debug.Log($"  - 筛选项数量: {model.FilterItems.Value?.Count ?? 0}");
                    Debug.Log($"  - 加载状态: {model.IsLoading.Value}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[EnergyDashboardPanelTest] 获取架构状态时出错: {ex.Message}");
            }
        }
    }
}
