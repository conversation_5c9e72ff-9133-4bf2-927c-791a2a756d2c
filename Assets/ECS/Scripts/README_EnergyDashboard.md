# 能源数据看板系统 (Energy Dashboard System)

基于QFramework架构和XCharts插件的能源数据可视化看板MVP实现。

## 系统架构

本系统严格遵循QFramework的MVC架构模式：

### 1. Model层 (数据模型)
- **EnergyDashboardModel**: 唯一的数据源，存储所有看板数据
- **数据结构**:
  - `KpiData`: KPI指标数据（总功率、总消耗、效率、碳排放）
  - `TimeSeriesChartData`: 时间序列图表数据
  - `SimpleFilterItem`: 筛选项数据
  - `EnergyFilterData`: 筛选条件数据

### 2. View层 (视图控制)
- **EnergyDashboardPanel**: 主面板，负责UI显示和用户交互
- **FilterItemComponent**: 筛选项组件，用于动态生成筛选UI

### 3. System层 (业务逻辑)
- **EnergyDataSystem**: 处理所有数据相关的业务逻辑
- 负责数据生成、筛选、更新等操作

### 4. Command层 (命令处理)
- **InitializeEnergyDataCommand**: 初始化数据命令
- **RefreshEnergyDataCommand**: 刷新数据命令
- **UpdateFilterSelectionCommand**: 更新筛选选择命令
- **UpdateTimeRangeCommand**: 更新时间范围命令

### 5. Architecture层 (架构注册)
- **EnergyDashboardArchitecture**: 注册所有Model和System

## 功能特性

### KPI卡片显示
- 总功率 (kW)
- 总消耗 (kWh)
- 效率 (%)
- 碳排放 (kg)
- 每个指标都显示数值和趋势（上升/下降百分比）

### 图表可视化
- **柱状图**: 功率数据时间序列
- **折线图**: 消耗数据时间序列
- **饼图**: 效率分布
- **仪表盘**: 当前效率指示

### 交互功能
- **时间范围选择**: 今天、本周、本月
- **设备/区域筛选**: 动态筛选项，支持多选
- **数据刷新**: 手动刷新数据
- **加载状态**: 显示数据加载进度

## 使用方法

### 1. 场景设置
1. 在场景中创建一个空GameObject
2. 添加`EnergyDashboardPanelTest`脚本
3. 运行场景，系统会自动初始化架构并打开面板

### 2. UI预制体设置
创建EnergyDashboardPanel预制体，包含以下UI组件：

#### KPI区域
- `totalPowerValue` - Text组件，显示总功率数值
- `totalPowerTrend` - Text组件，显示总功率趋势
- `totalConsumptionValue` - Text组件，显示总消耗数值
- `totalConsumptionTrend` - Text组件，显示总消耗趋势
- `efficiencyValue` - Text组件，显示效率数值
- `efficiencyTrend` - Text组件，显示效率趋势
- `carbonEmissionValue` - Text组件，显示碳排放数值
- `carbonEmissionTrend` - Text组件，显示碳排放趋势

#### 图表区域
- `powerChart` - BarChart组件 (XCharts)
- `consumptionChart` - LineChart组件 (XCharts)
- `efficiencyChart` - PieChart组件 (XCharts)
- `gaugeChart` - GaugeChart组件 (XCharts)

#### 控制区域
- `refreshButton` - Button组件，刷新数据
- `todayButton` - Button组件，选择今天
- `weekButton` - Button组件，选择本周
- `monthButton` - Button组件，选择本月
- `filterItemParent` - Transform组件，筛选项容器
- `filterItemPrefab` - GameObject，筛选项预制体

#### 其他
- `loadingPanel` - GameObject，加载状态面板

### 3. 筛选项预制体设置
创建FilterItem预制体，包含：
- Text组件：显示筛选项名称
- Toggle组件：选择状态
- 可选：添加`FilterItemComponent`脚本以获得更好的视觉效果

## 数据流程

1. **初始化**: `EnergyDashboardPanelTest`初始化架构
2. **面板打开**: `EnergyDashboardPanel.OnOpen()`被调用
3. **数据加载**: 发送`InitializeEnergyDataCommand`
4. **模型更新**: `EnergyDashboardModel.LoadMockData()`生成假数据
5. **事件通知**: 发送`EnergyDataUpdatedEvent`
6. **UI更新**: Panel监听事件，更新所有UI组件

## 扩展指南

### 添加新的KPI指标
1. 在`KpiData`类中添加新的`KpiItem`属性
2. 在`EnergyDashboardModel.LoadMockData()`中生成对应数据
3. 在`EnergyDashboardPanel`中添加对应的UI组件引用
4. 在`UpdateKpiUI()`方法中添加更新逻辑

### 添加新的图表类型
1. 在Panel中添加对应的XCharts组件引用
2. 在`UpdateChartsUI()`方法中添加图表更新逻辑
3. 根据需要在Model中添加对应的数据结构

### 添加新的筛选条件
1. 在`EnergyFilterData`中添加新的筛选属性
2. 创建对应的Command类
3. 在`EnergyDataSystem`中添加处理逻辑
4. 在Panel中添加对应的UI控件

## 注意事项

1. **数据来源**: 当前使用模拟数据，实际项目中需要替换为真实API调用
2. **性能优化**: 大量数据时考虑分页或虚拟化
3. **错误处理**: 添加网络错误、数据异常等错误处理逻辑
4. **国际化**: 考虑多语言支持
5. **主题切换**: 支持明暗主题切换

## 测试

使用`EnergyDashboardPanelTest`脚本进行功能测试：
- 按T键打开面板
- 使用Context Menu测试各种功能
- 查看Console输出了解系统状态
