using System;
using System.Collections;
using UnityEngine;
using QFramework;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 异步操作辅助类
    /// 提供简单的延迟执行功能，用于模拟异步数据加载
    /// </summary>
    public class AsyncHelper : Mono<PERSON><PERSON><PERSON><PERSON>, IController
    {
        private static AsyncHelper _instance;
        
        public static AsyncHelper Instance
        {
            get
            {
                if (_instance == null)
                {
                    var go = new GameObject("AsyncHelper");
                    _instance = go.AddComponent<AsyncHelper>();
                    DontDestroyOnLoad(go);
                }
                return _instance;
            }
        }

        public IArchitecture GetArchitecture()
        {
            return EnergyDashboardArchitecture.Interface;
        }

        /// <summary>
        /// 延迟执行操作
        /// </summary>
        /// <param name="delay">延迟时间（秒）</param>
        /// <param name="callback">回调函数</param>
        public static void DelayCall(float delay, Action callback)
        {
            Instance.StartCoroutine(Instance.DelayCallCoroutine(delay, callback));
        }

        /// <summary>
        /// 延迟执行协程
        /// </summary>
        private IEnumerator DelayCallCoroutine(float delay, Action callback)
        {
            yield return new WaitForSeconds(delay);
            callback?.Invoke();
        }

        /// <summary>
        /// 下一帧执行
        /// </summary>
        /// <param name="callback">回调函数</param>
        public static void NextFrame(Action callback)
        {
            Instance.StartCoroutine(Instance.NextFrameCoroutine(callback));
        }

        /// <summary>
        /// 下一帧执行协程
        /// </summary>
        private IEnumerator NextFrameCoroutine(Action callback)
        {
            yield return null;
            callback?.Invoke();
        }

        /// <summary>
        /// 等待指定帧数后执行
        /// </summary>
        /// <param name="frameCount">等待帧数</param>
        /// <param name="callback">回调函数</param>
        public static void DelayFrames(int frameCount, Action callback)
        {
            Instance.StartCoroutine(Instance.DelayFramesCoroutine(frameCount, callback));
        }

        /// <summary>
        /// 等待指定帧数后执行协程
        /// </summary>
        private IEnumerator DelayFramesCoroutine(int frameCount, Action callback)
        {
            for (int i = 0; i < frameCount; i++)
            {
                yield return null;
            }
            callback?.Invoke();
        }

        void OnDestroy()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }
}
