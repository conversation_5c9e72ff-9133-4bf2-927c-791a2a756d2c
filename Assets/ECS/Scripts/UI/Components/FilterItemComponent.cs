using UnityEngine;
using UnityEngine.UI;
using EnergyDashboard;

namespace QFramework.ShiCaiChang.LHJ
{
    /// <summary>
    /// 筛选项组件 - 用于筛选项预制体
    /// </summary>
    public class FilterItemComponent : MonoBehaviour
    {
        [Header("UI组件")]
        [SerializeField] private Text nameText;
        [SerializeField] private Toggle toggle;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image iconImage;

        [Header("样式设置")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color selectedColor = Color.cyan;
        [SerializeField] private Sprite areaIcon;
        [SerializeField] private Sprite deviceIcon;

        private SimpleFilterItem mFilterItem;
        private System.Action<string, bool> mOnSelectionChanged;

        /// <summary>
        /// 初始化筛选项
        /// </summary>
        /// <param name="filterItem">筛选项数据</param>
        /// <param name="onSelectionChanged">选择状态改变回调</param>
        public void Initialize(SimpleFilterItem filterItem, System.Action<string, bool> onSelectionChanged)
        {
            mFilterItem = filterItem;
            mOnSelectionChanged = onSelectionChanged;

            // 设置名称
            if (nameText != null)
            {
                nameText.text = filterItem.Name;
            }

            // 设置图标
            if (iconImage != null)
            {
                iconImage.sprite = filterItem.Type == FilterItemType.Area ? areaIcon : deviceIcon;
            }

            // 设置切换状态
            if (toggle != null)
            {
                toggle.isOn = filterItem.IsSelected;
                toggle.onValueChanged.AddListener(OnToggleValueChanged);
            }

            // 更新视觉状态
            UpdateVisualState();
        }

        private void OnToggleValueChanged(bool isOn)
        {
            if (mFilterItem != null)
            {
                mFilterItem.IsSelected = isOn;
                UpdateVisualState();
                
                // 通知外部选择状态改变
                mOnSelectionChanged?.Invoke(mFilterItem.Id, isOn);
            }
        }

        private void UpdateVisualState()
        {
            if (mFilterItem == null) return;

            // 更新背景颜色
            if (backgroundImage != null)
            {
                backgroundImage.color = mFilterItem.IsSelected ? selectedColor : normalColor;
            }

            // 更新文本颜色
            if (nameText != null)
            {
                nameText.color = mFilterItem.IsSelected ? Color.white : Color.black;
            }
        }

        /// <summary>
        /// 更新筛选项数据
        /// </summary>
        /// <param name="filterItem">新的筛选项数据</param>
        public void UpdateData(SimpleFilterItem filterItem)
        {
            mFilterItem = filterItem;

            if (nameText != null)
            {
                nameText.text = filterItem.Name;
            }

            if (toggle != null)
            {
                toggle.onValueChanged.RemoveListener(OnToggleValueChanged);
                toggle.isOn = filterItem.IsSelected;
                toggle.onValueChanged.AddListener(OnToggleValueChanged);
            }

            if (iconImage != null)
            {
                iconImage.sprite = filterItem.Type == FilterItemType.Area ? areaIcon : deviceIcon;
            }

            UpdateVisualState();
        }

        private void OnDestroy()
        {
            // 清理事件监听
            if (toggle != null)
            {
                toggle.onValueChanged.RemoveListener(OnToggleValueChanged);
            }
        }
    }
}
