using UnityEngine;
using UnityEngine.UI;
using QFramework;
using System.Collections.Generic;
using XCharts.Runtime;
using EnergyDashboard;
using System;
using System.Linq;

namespace QFramework.ShiCaiChang.LHJ
{
	public class EnergyDashboardPanelData : UIPanelData
	{
	}

	/// <summary>
	/// 能源看板面板 - MVP架构中的View层
	/// 负责界面显示和用户交互，通过QFramework架构与Model和System通信
	/// </summary>
	public partial class EnergyDashboardPanel : UIPanel, IController
	{
		[Header("KPI卡片区域")]
		[SerializeField][Tooltip("总功率数值显示")] private Text totalPowerValue;
		[SerializeField][Tooltip("总功率趋势显示（上升/下降百分比）")] private Text totalPowerTrend;
		[SerializeField][Tooltip("总消耗数值显示")] private Text totalConsumptionValue;
		[SerializeField][Tooltip("总消耗趋势显示（上升/下降百分比）")] private Text totalConsumptionTrend;
		[SerializeField][Tooltip("效率数值显示")] private Text efficiencyValue;
		[SerializeField][Tooltip("效率趋势显示（上升/下降百分比）")] private Text efficiencyTrend;
		[SerializeField][Tooltip("碳排放数值显示")] private Text carbonEmissionValue;
		[SerializeField][Tooltip("碳排放趋势显示（上升/下降百分比）")] private Text carbonEmissionTrend;

		[Header("图表区域")]
		[SerializeField][Tooltip("功率柱状图")] private BarChart powerChart;
		[SerializeField][Tooltip("消耗折线图")] private LineChart consumptionChart;
		[SerializeField][Tooltip("效率饼图")] private PieChart efficiencyChart;
		[SerializeField][Tooltip("效率仪表盘（环形图）")] private RingChart gaugeChart;

		[Header("筛选区域")]
		[SerializeField][Tooltip("刷新数据按钮")] private Button refreshButton;
		[SerializeField][Tooltip("今日数据筛选按钮")] private Button todayButton;
		[SerializeField][Tooltip("本周数据筛选按钮")] private Button weekButton;
		[SerializeField][Tooltip("本月数据筛选按钮")] private Button monthButton;
		[SerializeField][Tooltip("筛选项容器")] private Transform filterItemParent;
		[SerializeField][Tooltip("筛选项预制体")] private GameObject filterItemPrefab;

		[Header("加载状态")]
		[SerializeField][Tooltip("加载中面板")] private GameObject loadingPanel;
		private IEnergyDashboardModel mModel;
		private List<GameObject> mFilterItemObjects = new List<GameObject>();

		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as EnergyDashboardPanelData ?? new EnergyDashboardPanelData();
		}

		protected override void OnOpen(IUIData uiData = null)
		{
			// 初始化架构
			EnergyDashboardArchitecture.InitArchitecture();

			// 获取模型引用
			mModel = this.GetModel<IEnergyDashboardModel>();

			// 绑定UI事件
			BindUIEvents();

			// 监听数据更新事件
			this.RegisterEvent<EnergyDataUpdatedEvent>(OnDataUpdated);

			// 监听模型数据变化
			mModel.FilterItems.RegisterWithInitValue(OnFilterItemsChanged);
			mModel.KpiData.RegisterWithInitValue(OnKpiDataChanged);
			mModel.ChartData.RegisterWithInitValue(OnChartDataChanged);
			mModel.IsLoading.RegisterWithInitValue(OnLoadingStateChanged);

			// 初始化数据
			this.SendCommand<InitializeEnergyDataCommand>();
		}

		protected override void OnShow()
		{
			// 面板显示时的逻辑
		}

		protected override void OnHide()
		{
			// 面板隐藏时的逻辑
		}

		protected override void OnClose()
		{
			// 清理数据绑定
			this.UnRegisterEvent<EnergyDataUpdatedEvent>(OnDataUpdated);

			// 清理筛选项UI
			ClearFilterItems();
		}

		public IArchitecture GetArchitecture()
		{
			return EnergyDashboardArchitecture.Interface;
		}

		#region UI事件绑定

		private void BindUIEvents()
		{
			// 刷新按钮
			if (refreshButton != null)
			{
				refreshButton.onClick.AddListener(() => {
					this.SendCommand<RefreshEnergyDataCommand>();
				});
			}

			// 时间范围按钮
			if (todayButton != null)
			{
				todayButton.onClick.AddListener(() => {
					var today = DateTime.Now.Date;
					this.SendCommand(new UpdateTimeRangeCommand(today, today.AddDays(1)));
				});
			}

			if (weekButton != null)
			{
				weekButton.onClick.AddListener(() => {
					var today = DateTime.Now.Date;
					var weekStart = today.AddDays(-(int)today.DayOfWeek);
					this.SendCommand(new UpdateTimeRangeCommand(weekStart, weekStart.AddDays(7)));
				});
			}

			if (monthButton != null)
			{
				monthButton.onClick.AddListener(() => {
					var today = DateTime.Now.Date;
					var monthStart = new DateTime(today.Year, today.Month, 1);
					var monthEnd = monthStart.AddMonths(1);
					this.SendCommand(new UpdateTimeRangeCommand(monthStart, monthEnd));
				});
			}
		}

		#endregion

		#region 数据更新回调

		private void OnDataUpdated(EnergyDataUpdatedEvent e)
		{
			Debug.Log("[EnergyDashboardPanel] 收到数据更新事件");
		}

		private void OnFilterItemsChanged(List<SimpleFilterItem> filterItems)
		{
			UpdateFilterItemsUI(filterItems);
		}

		private void OnKpiDataChanged(KpiData kpiData)
		{
			UpdateKpiUI(kpiData);
		}

		private void OnChartDataChanged(TimeSeriesChartData chartData)
		{
			UpdateChartsUI(chartData);
		}

		private void OnLoadingStateChanged(bool isLoading)
		{
			if (loadingPanel != null)
			{
				loadingPanel.SetActive(isLoading);
			}
		}

		#endregion

		#region UI更新方法

		private void UpdateKpiUI(KpiData kpiData)
		{
			if (kpiData == null) return;

			// 更新总功率
			if (totalPowerValue != null && kpiData.TotalPower != null)
			{
				totalPowerValue.text = $"{kpiData.TotalPower.Value:F1} {kpiData.TotalPower.Unit}";
			}
			if (totalPowerTrend != null && kpiData.TotalPower != null)
			{
				var trend = kpiData.TotalPower.Trend;
				var trendText = trend > 0 ? $"↑{trend:F1}%" : $"↓{Math.Abs(trend):F1}%";
				totalPowerTrend.text = trendText;
				totalPowerTrend.color = trend > 0 ? Color.green : Color.red;
			}

			// 更新总消耗
			if (totalConsumptionValue != null && kpiData.TotalConsumption != null)
			{
				totalConsumptionValue.text = $"{kpiData.TotalConsumption.Value:F1} {kpiData.TotalConsumption.Unit}";
			}
			if (totalConsumptionTrend != null && kpiData.TotalConsumption != null)
			{
				var trend = kpiData.TotalConsumption.Trend;
				var trendText = trend > 0 ? $"↑{trend:F1}%" : $"↓{Math.Abs(trend):F1}%";
				totalConsumptionTrend.text = trendText;
				totalConsumptionTrend.color = trend > 0 ? Color.red : Color.green; // 消耗降低是好事
			}

			// 更新效率
			if (efficiencyValue != null && kpiData.Efficiency != null)
			{
				efficiencyValue.text = $"{kpiData.Efficiency.Value:F1} {kpiData.Efficiency.Unit}";
			}
			if (efficiencyTrend != null && kpiData.Efficiency != null)
			{
				var trend = kpiData.Efficiency.Trend;
				var trendText = trend > 0 ? $"↑{trend:F1}%" : $"↓{Math.Abs(trend):F1}%";
				efficiencyTrend.text = trendText;
				efficiencyTrend.color = trend > 0 ? Color.green : Color.red;
			}

			// 更新碳排放
			if (carbonEmissionValue != null && kpiData.CarbonEmission != null)
			{
				carbonEmissionValue.text = $"{kpiData.CarbonEmission.Value:F1} {kpiData.CarbonEmission.Unit}";
			}
			if (carbonEmissionTrend != null && kpiData.CarbonEmission != null)
			{
				var trend = kpiData.CarbonEmission.Trend;
				var trendText = trend > 0 ? $"↑{trend:F1}%" : $"↓{Math.Abs(trend):F1}%";
				carbonEmissionTrend.text = trendText;
				carbonEmissionTrend.color = trend > 0 ? Color.red : Color.green; // 排放降低是好事
			}
		}

		private void UpdateChartsUI(TimeSeriesChartData chartData)
		{
			if (chartData == null) return;

			// 更新柱状图 - 功率数据
			if (powerChart != null)
			{
				powerChart.ClearData();

				// 设置X轴数据
				for (int i = 0; i < chartData.Categories.Count; i++)
				{
					powerChart.AddXAxisData(chartData.Categories[i]);
				}

				// 添加功率系列数据
				for (int i = 0; i < chartData.PowerSeries.Count; i++)
				{
					powerChart.AddData(0, chartData.PowerSeries[i]);
				}

				powerChart.RefreshChart();
			}

			// 更新折线图 - 消耗数据
			if (consumptionChart != null)
			{
				consumptionChart.ClearData();

				// 设置X轴数据
				for (int i = 0; i < chartData.Categories.Count; i++)
				{
					consumptionChart.AddXAxisData(chartData.Categories[i]);
				}

				// 添加消耗系列数据
				for (int i = 0; i < chartData.ConsumptionSeries.Count; i++)
				{
					consumptionChart.AddData(0, chartData.ConsumptionSeries[i]);
				}

				consumptionChart.RefreshChart();
			}

			// 更新饼图 - 效率数据（简化为几个区间）
			if (efficiencyChart != null)
			{
				efficiencyChart.ClearData();

				// 计算效率分布
				var avgEfficiency = chartData.EfficiencySeries.Count > 0 ?
					chartData.EfficiencySeries.Average() : 0f;

				efficiencyChart.AddData("高效率", Math.Max(0, avgEfficiency - 70));
				efficiencyChart.AddData("中等效率", Math.Min(30, Math.Max(0, 100 - avgEfficiency)));
				efficiencyChart.AddData("低效率", Math.Max(0, 70 - avgEfficiency));

				efficiencyChart.RefreshChart();
			}

			// 更新环形图 - 当前效率（作为仪表盘使用）
			if (gaugeChart != null && chartData.EfficiencySeries.Count > 0)
			{
				gaugeChart.ClearData();
				var currentEfficiency = chartData.EfficiencySeries.LastOrDefault();
				var maxValue = 100f; // 效率最大值为100%

				// 添加当前效率数据
				gaugeChart.AddData(0, currentEfficiency, maxValue, "当前效率");

				gaugeChart.RefreshChart();
			}
		}

		private void UpdateFilterItemsUI(List<SimpleFilterItem> filterItems)
		{
			if (filterItemParent == null || filterItemPrefab == null) return;

			// 清理现有的筛选项UI
			ClearFilterItems();

			// 创建新的筛选项UI
			foreach (var item in filterItems)
			{
				var itemObj = Instantiate(filterItemPrefab, filterItemParent);
				mFilterItemObjects.Add(itemObj);

				// 尝试使用FilterItemComponent
				var filterComponent = itemObj.GetComponent<FilterItemComponent>();
				if (filterComponent != null)
				{
					// 使用专用组件
					filterComponent.Initialize(item, (id, isSelected) => {
						this.SendCommand(new UpdateFilterSelectionCommand(id, isSelected));
					});
				}
				else
				{
					// 回退到基础组件
					var text = itemObj.GetComponentInChildren<Text>();
					var toggle = itemObj.GetComponentInChildren<Toggle>();

					if (text != null)
					{
						text.text = item.Name;
					}

					if (toggle != null)
					{
						toggle.isOn = item.IsSelected;

						// 绑定切换事件
						var itemId = item.Id; // 捕获当前项的ID
						toggle.onValueChanged.AddListener((isOn) => {
							this.SendCommand(new UpdateFilterSelectionCommand(itemId, isOn));
						});
					}
				}

				itemObj.SetActive(true);
			}
		}

		private void ClearFilterItems()
		{
			foreach (var obj in mFilterItemObjects)
			{
				if (obj != null)
				{
					DestroyImmediate(obj);
				}
			}
			mFilterItemObjects.Clear();
		}

		#endregion
	}
}
