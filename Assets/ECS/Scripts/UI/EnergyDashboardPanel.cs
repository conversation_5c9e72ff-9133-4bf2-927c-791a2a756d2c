using UnityEngine;
using UnityEngine.UI;
using QFramework;
using System.Collections.Generic;
using XCharts.Runtime;

namespace QFramework.ShiCaiChang.LHJ
{
	public class EnergyDashboardPanelData : UIPanelData
	{
	}

	/// <summary>
	/// 能源看板面板 - MVP架构中的View层
	/// 负责界面显示和用户交互，通过QFramework架构与Model和System通信
	/// </summary>
	public partial class EnergyDashboardPanel : UIPanel, IController
	{
		
	
		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as EnergyDashboardPanelData ?? new EnergyDashboardPanelData();

		
		}

		protected override void OnOpen(IUIData uiData = null)
		{
			// 面板打开时的逻辑
		}

		protected override void OnShow()
		{
			// 面板显示时的逻辑
		}

		protected override void OnHide()
		{
			// 面板隐藏时的逻辑
		}

		protected override void OnClose()
		{
			// 清理数据绑定
		
		}

		public IArchitecture GetArchitecture()
		{
			throw new System.NotImplementedException();
		}
	}
}
