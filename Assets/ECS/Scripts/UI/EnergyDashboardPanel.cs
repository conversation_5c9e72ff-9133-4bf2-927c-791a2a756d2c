using UnityEngine;
using UnityEngine.UI;
using QFramework;
using System.Collections.Generic;
using XCharts.Runtime;
using EnergyDashboard;
using System;
using System.Linq;

namespace QFramework.ShiCaiChang.LHJ
{
	public class EnergyDashboardPanelData : UIPanelData
	{
	}

	/// <summary>
	/// 能源看板面板 - MVP架构中的View层
	/// 负责界面显示和用户交互，通过QFramework架构与Model和System通信
	/// </summary>
	public partial class EnergyDashboardPanel : UIPanel, IController
	{
		[Header("上排数值型KPI模块")]
		// 1. 周期内总消耗
		[SerializeField][Tooltip("周期内总消耗 - 数值")] private Text totalConsumptionValue;
		[SerializeField][Tooltip("周期内总消耗 - 单位")] private Text totalConsumptionUnit;
		[SerializeField][Tooltip("周期内总消耗 - 趋势百分比")] private Text totalConsumptionTrend;
		[SerializeField][Tooltip("周期内总消耗 - 趋势图标")] private Image totalConsumptionTrendIcon;
		[SerializeField][Tooltip("周期内总消耗 - 平均日耗")] private Text totalConsumptionDaily;

		// 2. 预估电费
		[SerializeField][Tooltip("预估电费 - 数值")] private Text estimatedCostValue;
		[SerializeField][Tooltip("预估电费 - 单位")] private Text estimatedCostUnit;
		[SerializeField][Tooltip("预估电费 - 趋势百分比")] private Text estimatedCostTrend;
		[SerializeField][Tooltip("预估电费 - 趋势图标")] private Image estimatedCostTrendIcon;
		[SerializeField][Tooltip("预估电费 - 单价")] private Text estimatedCostPrice;

		// 3. 碳排放量
		[SerializeField][Tooltip("碳排放量 - 数值")] private Text carbonEmissionValue;
		[SerializeField][Tooltip("碳排放量 - 单位")] private Text carbonEmissionUnit;
		[SerializeField][Tooltip("碳排放量 - 趋势百分比")] private Text carbonEmissionTrend;
		[SerializeField][Tooltip("碳排放量 - 趋势图标")] private Image carbonEmissionTrendIcon;
		[SerializeField][Tooltip("碳排放量 - 系数")] private Text carbonEmissionFactor;

		// 4. 平均功率因数
		[SerializeField][Tooltip("平均功率因数 - 数值")] private Text powerFactorValue;
		[SerializeField][Tooltip("平均功率因数 - 单位")] private Text powerFactorUnit;
		[SerializeField][Tooltip("平均功率因数 - 趋势百分比")] private Text powerFactorTrend;
		[SerializeField][Tooltip("平均功率因数 - 趋势图标")] private Image powerFactorTrendIcon;
		[SerializeField][Tooltip("平均功率因数 - 目标")] private Text powerFactorTarget;

		[Header("下排状态型KPI模块")]
		// 5. 实时负荷率
		[SerializeField][Tooltip("实时负荷率 - 主要百分比")] private Text loadRateValue;
		[SerializeField][Tooltip("实时负荷率 - 当前负荷值")] private Text loadRateCurrent;
		[SerializeField][Tooltip("实时负荷率 - 设计容量值")] private Text loadRateCapacity;
		[SerializeField][Tooltip("实时负荷率 - 状态指示器")] private Image loadRateStatusIndicator;
		[SerializeField][Tooltip("实时负荷率 - 状态文本")] private Text loadRateStatusText;

		// 6. 设备运行状态
		[SerializeField][Tooltip("设备运行状态 - 正常设备数")] private Text deviceStatusNormal;
		[SerializeField][Tooltip("设备运行状态 - 预警设备数")] private Text deviceStatusWarning;
		[SerializeField][Tooltip("设备运行状态 - 故障设备数")] private Text deviceStatusFault;
		[SerializeField][Tooltip("设备运行状态 - 状态指示器")] private Image deviceStatusIndicator;
		[SerializeField][Tooltip("设备运行状态 - 状态文本")] private Text deviceStatusText;

		// 7. 能效等级分布
		[SerializeField][Tooltip("能效等级分布 - A级进度条")] private Slider efficiencyGradeA;
		[SerializeField][Tooltip("能效等级分布 - B级进度条")] private Slider efficiencyGradeB;
		[SerializeField][Tooltip("能效等级分布 - C级进度条")] private Slider efficiencyGradeC;
		[SerializeField][Tooltip("能效等级分布 - 状态指示器")] private Image efficiencyGradeStatusIndicator;
		[SerializeField][Tooltip("能效等级分布 - 状态文本")] private Text efficiencyGradeStatusText;

		// 8. 节能目标完成度
		[SerializeField][Tooltip("节能目标完成度 - 当前完成百分比")] private Text energySavingProgress;
		[SerializeField][Tooltip("节能目标完成度 - 已节约kWh")] private Text energySavingSaved;
		[SerializeField][Tooltip("节能目标完成度 - 目标kWh")] private Text energySavingTarget;
		[SerializeField][Tooltip("节能目标完成度 - 状态指示器")] private Image energySavingStatusIndicator;
		[SerializeField][Tooltip("节能目标完成度 - 状态文本")] private Text energySavingStatusText;

		[Header("图表区域")]
		[SerializeField][Tooltip("功率柱状图")] private BarChart powerChart;
		[SerializeField][Tooltip("消耗折线图")] private LineChart consumptionChart;
		[SerializeField][Tooltip("效率饼图")] private PieChart efficiencyChart;
		[SerializeField][Tooltip("效率仪表盘（环形图）")] private RingChart gaugeChart;

		[Header("筛选区域")]
		[SerializeField][Tooltip("刷新数据按钮")] private Button refreshButton;
		[SerializeField][Tooltip("今日数据筛选按钮")] private Button todayButton;
		[SerializeField][Tooltip("本周数据筛选按钮")] private Button weekButton;
		[SerializeField][Tooltip("本月数据筛选按钮")] private Button monthButton;
		[SerializeField][Tooltip("筛选项容器")] private Transform filterItemParent;
		[SerializeField][Tooltip("筛选项预制体")] private GameObject filterItemPrefab;

		[Header("加载状态")]
		[SerializeField][Tooltip("加载中面板")] private GameObject loadingPanel;
		private IEnergyDashboardModel mModel;
		private List<GameObject> mFilterItemObjects = new List<GameObject>();

		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as EnergyDashboardPanelData ?? new EnergyDashboardPanelData();
		}

		protected override void OnOpen(IUIData uiData = null)
		{
			// 初始化架构
			EnergyDashboardArchitecture.InitArchitecture();

			// 获取模型引用
			mModel = this.GetModel<IEnergyDashboardModel>();

			// 绑定UI事件
			BindUIEvents();

			// 监听数据更新事件
			this.RegisterEvent<EnergyDataUpdatedEvent>(OnDataUpdated);

			// 监听模型数据变化
			mModel.FilterItems.RegisterWithInitValue(OnFilterItemsChanged);
			mModel.KpiData.RegisterWithInitValue(OnKpiDataChanged);
			mModel.ChartData.RegisterWithInitValue(OnChartDataChanged);
			mModel.IsLoading.RegisterWithInitValue(OnLoadingStateChanged);

			// 初始化数据
			this.SendCommand<InitializeEnergyDataCommand>();
		}

		protected override void OnShow()
		{
			// 面板显示时的逻辑
		}

		protected override void OnHide()
		{
			// 面板隐藏时的逻辑
		}

		protected override void OnClose()
		{
			// 清理数据绑定
			this.UnRegisterEvent<EnergyDataUpdatedEvent>(OnDataUpdated);

			// 清理筛选项UI
			ClearFilterItems();
		}

		public IArchitecture GetArchitecture()
		{
			return EnergyDashboardArchitecture.Interface;
		}

		#region UI事件绑定

		private void BindUIEvents()
		{
			// 刷新按钮
			if (refreshButton != null)
			{
				refreshButton.onClick.AddListener(() => {
					this.SendCommand<RefreshEnergyDataCommand>();
				});
			}

			// 时间范围按钮
			if (todayButton != null)
			{
				todayButton.onClick.AddListener(() => {
					var today = DateTime.Now.Date;
					this.SendCommand(new UpdateTimeRangeCommand(today, today.AddDays(1)));
				});
			}

			if (weekButton != null)
			{
				weekButton.onClick.AddListener(() => {
					var today = DateTime.Now.Date;
					var weekStart = today.AddDays(-(int)today.DayOfWeek);
					this.SendCommand(new UpdateTimeRangeCommand(weekStart, weekStart.AddDays(7)));
				});
			}

			if (monthButton != null)
			{
				monthButton.onClick.AddListener(() => {
					var today = DateTime.Now.Date;
					var monthStart = new DateTime(today.Year, today.Month, 1);
					var monthEnd = monthStart.AddMonths(1);
					this.SendCommand(new UpdateTimeRangeCommand(monthStart, monthEnd));
				});
			}
		}

		#endregion

		#region 数据更新回调

		private void OnDataUpdated(EnergyDataUpdatedEvent e)
		{
			Debug.Log("[EnergyDashboardPanel] 收到数据更新事件");
		}

		private void OnFilterItemsChanged(List<SimpleFilterItem> filterItems)
		{
			UpdateFilterItemsUI(filterItems);
		}

		private void OnKpiDataChanged(KpiData kpiData)
		{
			UpdateKpiUI(kpiData);
		}

		private void OnChartDataChanged(TimeSeriesChartData chartData)
		{
			UpdateChartsUI(chartData);
		}

		private void OnLoadingStateChanged(bool isLoading)
		{
			if (loadingPanel != null)
			{
				loadingPanel.SetActive(isLoading);
			}
		}

		#endregion

		#region UI更新方法

		private void UpdateKpiUI(KpiData kpiData)
		{
			if (kpiData == null) return;

			// 更新上排数值型KPI模块
			UpdateNumericKpiModule(kpiData.TotalConsumption,
				totalConsumptionValue, totalConsumptionUnit, totalConsumptionTrend,
				totalConsumptionTrendIcon, totalConsumptionDaily);

			UpdateNumericKpiModule(kpiData.EstimatedCost,
				estimatedCostValue, estimatedCostUnit, estimatedCostTrend,
				estimatedCostTrendIcon, estimatedCostPrice);

			UpdateNumericKpiModule(kpiData.CarbonEmission,
				carbonEmissionValue, carbonEmissionUnit, carbonEmissionTrend,
				carbonEmissionTrendIcon, carbonEmissionFactor);

			UpdateNumericKpiModule(kpiData.PowerFactor,
				powerFactorValue, powerFactorUnit, powerFactorTrend,
				powerFactorTrendIcon, powerFactorTarget);

			// 更新下排状态型KPI模块
			UpdateStatusKpiModule(kpiData.LoadRate,
				loadRateValue, loadRateCurrent, loadRateCapacity,
				loadRateStatusIndicator, loadRateStatusText);

			UpdateStatusKpiModule(kpiData.DeviceStatus,
				deviceStatusNormal, deviceStatusWarning, deviceStatusFault,
				deviceStatusIndicator, deviceStatusText);

			UpdateEfficiencyGradeModule(kpiData.EfficiencyGrade);

			UpdateStatusKpiModule(kpiData.EnergySaving,
				energySavingProgress, energySavingSaved, energySavingTarget,
				energySavingStatusIndicator, energySavingStatusText);
		}

		private void UpdateNumericKpiModule(NumericKpiItem kpiItem, Text valueText, Text unitText,
			Text trendText, Image trendIcon, Text auxiliaryText)
		{
			if (kpiItem == null) return;

			// 更新主要数值和单位
			if (valueText != null)
			{
				valueText.text = kpiItem.Value.ToString("F1");
			}
			if (unitText != null)
			{
				unitText.text = kpiItem.Unit;
			}

			// 更新趋势百分比和图标
			if (trendText != null)
			{
				var trend = kpiItem.Trend;
				trendText.text = $"{Math.Abs(trend):F1}%";
				trendText.color = trend > 0 ? Color.red : Color.green;
			}
			if (trendIcon != null)
			{
				// 这里应该设置上升/下降的图标精灵
				// trendIcon.sprite = kpiItem.Trend > 0 ? upArrowSprite : downArrowSprite;
				trendIcon.color = kpiItem.Trend > 0 ? Color.red : Color.green;
			}

			// 更新辅助信息
			if (auxiliaryText != null && !string.IsNullOrEmpty(kpiItem.AuxiliaryInfo))
			{
				auxiliaryText.text = $"{kpiItem.AuxiliaryLabel}: {kpiItem.AuxiliaryInfo}";
			}
		}

		private void UpdateStatusKpiModule(StatusKpiItem kpiItem, Text mainText, Text secondaryText,
			Text tertiaryText, Image statusIndicator, Text statusText)
		{
			if (kpiItem == null) return;

			// 更新主要数值
			if (mainText != null)
			{
				mainText.text = $"{kpiItem.MainValue:F1}{kpiItem.MainUnit}";
			}

			// 更新次要数值
			if (secondaryText != null)
			{
				secondaryText.text = $"{kpiItem.SecondaryValue:F1}{kpiItem.SecondaryUnit}";
			}

			// 更新第三数值
			if (tertiaryText != null)
			{
				tertiaryText.text = $"{kpiItem.TertiaryValue:F1}{kpiItem.TertiaryUnit}";
			}

			// 更新状态指示器和文本
			UpdateStatusIndicator(statusIndicator, statusText, kpiItem.Status, kpiItem.StatusText);
		}

		private void UpdateEfficiencyGradeModule(StatusKpiItem kpiItem)
		{
			if (kpiItem == null) return;

			// 更新A级进度条
			if (efficiencyGradeA != null)
			{
				efficiencyGradeA.value = kpiItem.MainValue / 100f;
			}

			// 更新B级进度条
			if (efficiencyGradeB != null)
			{
				efficiencyGradeB.value = kpiItem.SecondaryValue / 100f;
			}

			// 更新C级进度条
			if (efficiencyGradeC != null)
			{
				efficiencyGradeC.value = kpiItem.TertiaryValue / 100f;
			}

			// 更新状态指示器
			UpdateStatusIndicator(efficiencyGradeStatusIndicator, efficiencyGradeStatusText,
				kpiItem.Status, kpiItem.StatusText);
		}

		private void UpdateStatusIndicator(Image indicator, Text statusText, KpiStatus status, string text)
		{
			if (indicator != null)
			{
				indicator.color = GetStatusColor(status);
			}

			if (statusText != null)
			{
				statusText.text = text;
				statusText.color = GetStatusColor(status);
			}
		}

		private Color GetStatusColor(KpiStatus status)
		{
			switch (status)
			{
				case KpiStatus.Normal:
					return Color.green;
				case KpiStatus.Attention:
					return Color.yellow;
				case KpiStatus.Excellent:
					return Color.blue;
				case KpiStatus.Target:
					return Color.cyan;
				default:
					return Color.white;
			}
		}

		private void UpdateChartsUI(TimeSeriesChartData chartData)
		{
			if (chartData == null) return;

			// 更新柱状图 - 功率数据
			if (powerChart != null)
			{
				powerChart.ClearData();

				// 设置X轴数据
				for (int i = 0; i < chartData.Categories.Count; i++)
				{
					powerChart.AddXAxisData(chartData.Categories[i]);
				}

				// 添加功率系列数据
				for (int i = 0; i < chartData.PowerSeries.Count; i++)
				{
					powerChart.AddData(0, chartData.PowerSeries[i]);
				}

				powerChart.RefreshChart();
			}

			// 更新折线图 - 消耗数据
			if (consumptionChart != null)
			{
				consumptionChart.ClearData();

				// 设置X轴数据
				for (int i = 0; i < chartData.Categories.Count; i++)
				{
					consumptionChart.AddXAxisData(chartData.Categories[i]);
				}

				// 添加消耗系列数据
				for (int i = 0; i < chartData.ConsumptionSeries.Count; i++)
				{
					consumptionChart.AddData(0, chartData.ConsumptionSeries[i]);
				}

				consumptionChart.RefreshChart();
			}

			// 更新饼图 - 效率数据（简化为几个区间）
			if (efficiencyChart != null)
			{
				efficiencyChart.ClearData();

				// 计算效率分布
				var avgEfficiency = chartData.EfficiencySeries.Count > 0 ?
					chartData.EfficiencySeries.Average() : 0f;

				efficiencyChart.AddData("高效率", Math.Max(0, avgEfficiency - 70));
				efficiencyChart.AddData("中等效率", Math.Min(30, Math.Max(0, 100 - avgEfficiency)));
				efficiencyChart.AddData("低效率", Math.Max(0, 70 - avgEfficiency));

				efficiencyChart.RefreshChart();
			}

			// 更新环形图 - 当前效率（作为仪表盘使用）
			if (gaugeChart != null && chartData.EfficiencySeries.Count > 0)
			{
				gaugeChart.ClearData();
				var currentEfficiency = chartData.EfficiencySeries.LastOrDefault();
				var maxValue = 100f; // 效率最大值为100%

				// 添加当前效率数据
				gaugeChart.AddData(0, currentEfficiency, maxValue, "当前效率");

				gaugeChart.RefreshChart();
			}
		}

		private void UpdateFilterItemsUI(List<SimpleFilterItem> filterItems)
		{
			if (filterItemParent == null || filterItemPrefab == null) return;

			// 清理现有的筛选项UI
			ClearFilterItems();

			// 创建新的筛选项UI
			foreach (var item in filterItems)
			{
				var itemObj = Instantiate(filterItemPrefab, filterItemParent);
				mFilterItemObjects.Add(itemObj);

				// 尝试使用FilterItemComponent
				var filterComponent = itemObj.GetComponent<FilterItemComponent>();
				if (filterComponent != null)
				{
					// 使用专用组件
					filterComponent.Initialize(item, (id, isSelected) => {
						this.SendCommand(new UpdateFilterSelectionCommand(id, isSelected));
					});
				}
				else
				{
					// 回退到基础组件
					var text = itemObj.GetComponentInChildren<Text>();
					var toggle = itemObj.GetComponentInChildren<Toggle>();

					if (text != null)
					{
						text.text = item.Name;
					}

					if (toggle != null)
					{
						toggle.isOn = item.IsSelected;

						// 绑定切换事件
						var itemId = item.Id; // 捕获当前项的ID
						toggle.onValueChanged.AddListener((isOn) => {
							this.SendCommand(new UpdateFilterSelectionCommand(itemId, isOn));
						});
					}
				}

				itemObj.SetActive(true);
			}
		}

		private void ClearFilterItems()
		{
			foreach (var obj in mFilterItemObjects)
			{
				if (obj != null)
				{
					DestroyImmediate(obj);
				}
			}
			mFilterItemObjects.Clear();
		}

		#endregion
	}
}
