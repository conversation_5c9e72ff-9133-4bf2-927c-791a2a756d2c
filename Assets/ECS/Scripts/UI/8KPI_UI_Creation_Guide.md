# 8个KPI模块UI创建指南

## 📊 重新设计概述

已将原来的4个KPI卡片扩展为8个专业的KPI模块：

### 上排数值型KPI模块（4个）
1. **周期内总消耗** - 8760.3 kWh ↓2.1% (平均日耗: 365.0)
2. **预估电费** - 6132.2 元 ↑3.5% (单价: 0.70)
3. **碳排放量** - 5486.2 kg ↓8.5% (系数: 0.626)
4. **平均功率因数** - 0.92 ↑1.8% (目标: 0.95)

### 下排状态型KPI模块（4个）
5. **实时负荷率** - 78.5% (1570.0kW / 2000.0kW) 🟢正常
6. **设备运行状态** - 正常45台/预警3台/故障1台 🟡注意
7. **能效等级分布** - A级65%/B级25%/C级10% 🔵优秀
8. **节能目标完成度** - 85.2% (已节约1278.5kWh/目标1500.0kWh) 🔷达标

## 🏗️ UI组件绑定清单

### 上排数值型KPI模块组件 (每个模块5个组件)

#### 1. 周期内总消耗模块
- [ ] `totalConsumptionValue` (Text) - 数值显示
- [ ] `totalConsumptionUnit` (Text) - 单位显示
- [ ] `totalConsumptionTrend` (Text) - 趋势百分比
- [ ] `totalConsumptionTrendIcon` (Image) - 趋势图标
- [ ] `totalConsumptionDaily` (Text) - 平均日耗

#### 2. 预估电费模块
- [ ] `estimatedCostValue` (Text) - 数值显示
- [ ] `estimatedCostUnit` (Text) - 单位显示
- [ ] `estimatedCostTrend` (Text) - 趋势百分比
- [ ] `estimatedCostTrendIcon` (Image) - 趋势图标
- [ ] `estimatedCostPrice` (Text) - 单价

#### 3. 碳排放量模块
- [ ] `carbonEmissionValue` (Text) - 数值显示
- [ ] `carbonEmissionUnit` (Text) - 单位显示
- [ ] `carbonEmissionTrend` (Text) - 趋势百分比
- [ ] `carbonEmissionTrendIcon` (Image) - 趋势图标
- [ ] `carbonEmissionFactor` (Text) - 系数

#### 4. 平均功率因数模块
- [ ] `powerFactorValue` (Text) - 数值显示
- [ ] `powerFactorUnit` (Text) - 单位显示
- [ ] `powerFactorTrend` (Text) - 趋势百分比
- [ ] `powerFactorTrendIcon` (Image) - 趋势图标
- [ ] `powerFactorTarget` (Text) - 目标

### 下排状态型KPI模块组件 (每个模块5个组件)

#### 5. 实时负荷率模块
- [ ] `loadRateValue` (Text) - 主要百分比
- [ ] `loadRateCurrent` (Text) - 当前负荷值
- [ ] `loadRateCapacity` (Text) - 设计容量值
- [ ] `loadRateStatusIndicator` (Image) - 状态指示器
- [ ] `loadRateStatusText` (Text) - 状态文本

#### 6. 设备运行状态模块
- [ ] `deviceStatusNormal` (Text) - 正常设备数
- [ ] `deviceStatusWarning` (Text) - 预警设备数
- [ ] `deviceStatusFault` (Text) - 故障设备数
- [ ] `deviceStatusIndicator` (Image) - 状态指示器
- [ ] `deviceStatusText` (Text) - 状态文本

#### 7. 能效等级分布模块
- [ ] `efficiencyGradeA` (Slider) - A级进度条
- [ ] `efficiencyGradeB` (Slider) - B级进度条
- [ ] `efficiencyGradeC` (Slider) - C级进度条
- [ ] `efficiencyGradeStatusIndicator` (Image) - 状态指示器
- [ ] `efficiencyGradeStatusText` (Text) - 状态文本

#### 8. 节能目标完成度模块
- [ ] `energySavingProgress` (Text) - 完成百分比
- [ ] `energySavingSaved` (Text) - 已节约kWh
- [ ] `energySavingTarget` (Text) - 目标kWh
- [ ] `energySavingStatusIndicator` (Image) - 状态指示器
- [ ] `energySavingStatusText` (Text) - 状态文本

## 🎨 UI布局建议

### 上排数值型KPI模块布局
```
┌─────────────────────────────────────────────────────────────────────────┐
│  [消耗]     [电费]     [碳排放]   [功率因数]                              │
│  8760.3     6132.2     5486.2     0.92                                  │
│  kWh        元         kg         (无单位)                               │
│  ↓2.1%      ↑3.5%      ↓8.5%      ↑1.8%                                │
│  日耗:365.0 单价:0.70  系数:0.626  目标:0.95                             │
└─────────────────────────────────────────────────────────────────────────┘
```

### 下排状态型KPI模块布局
```
┌─────────────────────────────────────────────────────────────────────────┐
│  [负荷率]        [设备状态]      [能效等级]      [节能目标]              │
│  78.5%           正常:45台       A级:████████    85.2%                   │
│  1570.0kW        预警:3台        B级:████        已节约:1278.5kWh        │
│  /2000.0kW       故障:1台        C级:██          目标:1500.0kWh          │
│  🟢 正常         🟡 注意         🔵 优秀        🔷 达标                  │
└─────────────────────────────────────────────────────────────────────────┘
```

## 🔧 数据结构说明

### NumericKpiItem (数值型KPI)
- `Value`: 主要数值 (float)
- `Unit`: 单位 (string)
- `Trend`: 趋势百分比 (float, 正数上升，负数下降)
- `AuxiliaryInfo`: 辅助信息 (string)
- `AuxiliaryLabel`: 辅助信息标签 (string)

### StatusKpiItem (状态型KPI)
- `MainValue`: 主要数值 (float)
- `SecondaryValue`: 次要数值 (float)
- `TertiaryValue`: 第三数值 (float)
- `Status`: 状态枚举 (KpiStatus)
- `StatusText`: 状态文本 (string)

### KpiStatus 状态枚举
- `Normal`: 正常 (绿色)
- `Attention`: 注意 (黄色)
- `Excellent`: 优秀 (蓝色)
- `Target`: 达标 (青色)

## 🎯 关键特性

1. **趋势图标**: 使用专门的上升/下降箭头图片资源
2. **状态指示器**: 支持4种状态的颜色显示
3. **进度条**: 能效等级分布使用Slider组件
4. **响应式布局**: 上下两排，每排4个模块
5. **数据驱动**: 完全基于数据模型更新UI

## 📝 实现状态

✅ **已完成**:
- SerializeField字段定义 (40个组件引用)
- 数据模型结构 (NumericKpiItem, StatusKpiItem, KpiStatus)
- UI更新方法 (UpdateKpiUI, UpdateNumericKpiModule, UpdateStatusKpiModule)
- 模拟数据生成 (8个KPI模块的完整数据)

🔄 **待完成**:
- Unity预制体创建和组件绑定
- 趋势图标资源准备 (上升/下降箭头)
- 状态指示器图标资源
- UI样式和布局调整

## 🚀 下一步操作

1. 在Unity中创建8个KPI模块的UI预制体
2. 绑定所有40个SerializeField组件引用
3. 准备趋势图标和状态指示器的图片资源
4. 测试数据显示和状态切换效果
5. 调整UI样式和响应式布局
